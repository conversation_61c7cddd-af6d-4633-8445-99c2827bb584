(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[405],{8312:function(e,t,s){(window.__NEXT_P=window.__NEXT_P||[]).push(["/",function(){return s(7118)}])},7118:function(e,t,s){"use strict";let i;s.r(t),s.d(t,{default:function(){return $}});var r=s(5893),n=s(7294),a=s(7682),o=s.n(a),u={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};let l=e=>e.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase().trim(),c=(e,t)=>{let s=(0,n.forwardRef)((s,i)=>{let{color:r="currentColor",size:a=24,strokeWidth:o=2,absoluteStrokeWidth:c,className:h="",children:m,...g}=s;return(0,n.createElement)("svg",{ref:i,...u,width:a,height:a,stroke:r,strokeWidth:c?24*Number(o)/Number(a):o,className:["lucide","lucide-".concat(l(e)),h].join(" "),...g},[...t.map(e=>{let[t,s]=e;return(0,n.createElement)(t,s)}),...Array.isArray(m)?m:[m]])});return s.displayName="".concat(e),s},h=c("Brain",[["path",{d:"M9.5 2A2.5 2.5 0 0 1 12 4.5v15a2.5 2.5 0 0 1-4.96.44 2.5 2.5 0 0 1-2.96-3.08 3 3 0 0 1-.34-5.58 2.5 2.5 0 0 1 1.32-4.24 2.5 2.5 0 0 1 1.98-3A2.5 2.5 0 0 1 9.5 2Z",key:"1mhkh5"}],["path",{d:"M14.5 2A2.5 2.5 0 0 0 12 4.5v15a2.5 2.5 0 0 0 4.96.44 2.5 2.5 0 0 0 2.96-3.08 3 3 0 0 0 .34-5.58 2.5 2.5 0 0 0-1.32-4.24 2.5 2.5 0 0 0-1.98-3A2.5 2.5 0 0 0 14.5 2Z",key:"1d6s00"}]]),m=c("Zap",[["polygon",{points:"13 2 3 14 12 14 11 22 21 10 12 10 13 2",key:"45s27k"}]]),g=c("Heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),d=c("Download",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"7 10 12 15 17 10",key:"2ggqvy"}],["line",{x1:"12",x2:"12",y1:"15",y2:"3",key:"1vk2je"}]]),p=c("Upload",[["path",{d:"M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4",key:"ih7n3h"}],["polyline",{points:"17 8 12 3 7 8",key:"t8dd8p"}],["line",{x1:"12",x2:"12",y1:"3",y2:"15",key:"widbto"}]]),y=c("RotateCcw",[["path",{d:"M3 12a9 9 0 1 0 9-9 9.75 9.75 0 0 0-6.74 2.74L3 8",key:"1357e3"}],["path",{d:"M3 3v5h5",key:"1xhq8a"}]]),D=c("MessageCircle",[["path",{d:"m3 21 1.9-5.7a8.5 8.5 0 1 1 3.8 3.8z",key:"v2veuj"}]]),f=c("Coffee",[["path",{d:"M17 8h1a4 4 0 1 1 0 8h-1",key:"jx4kbh"}],["path",{d:"M3 8h14v9a4 4 0 0 1-4 4H7a4 4 0 0 1-4-4Z",key:"1bxrl0"}],["line",{x1:"6",x2:"6",y1:"2",y2:"4",key:"1cr9l3"}],["line",{x1:"10",x2:"10",y1:"2",y2:"4",key:"170wym"}],["line",{x1:"14",x2:"14",y1:"2",y2:"4",key:"1c5f70"}]]),x=c("Star",[["polygon",{points:"12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2",key:"8f66p6"}]]);class v{ensureDataDirectory(){try{localStorage.getItem("chitu00-initialized")||localStorage.setItem("chitu00-initialized",new Date().toISOString())}catch(e){console.warn("Failed to initialize data directory:",e)}}async withRetry(e,t){let s=null;for(let i=1;i<=this.maxRetries;i++)try{return await e()}catch(e){s=e,console.warn("Attempt ".concat(i,"/").concat(this.maxRetries," failed for ").concat(t,":"),e),i<this.maxRetries&&await this.delay(this.retryDelay*i)}throw Error("Failed after ".concat(this.maxRetries," attempts in ").concat(t,": ").concat(null==s?void 0:s.message))}delay(e){return new Promise(t=>setTimeout(t,e))}safeLocalStorageOperation(e,t,s){try{return e()}catch(e){return console.error("localStorage operation failed in ".concat(s,":"),e),t}}async storeConversation(e){return this.withRetry(async()=>{if(!e||!e.id||!e.userMessage)throw Error("Invalid conversation memory: missing required fields");let t=await this.getRecentConversations(1e3),s=t.findIndex(t=>t.id===e.id);s>=0?t[s]=e:t.push(e);let i=t.slice(-1e3);return this.safeLocalStorageOperation(()=>{localStorage.setItem("chitu00-conversations",JSON.stringify(i))},void 0,"storeConversation")},"storeConversation")}async getRecentConversations(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;return this.withRetry(async()=>{if(e<0)throw Error("Limit must be non-negative");let t=Math.min(Math.max(0,Math.floor(e)),1e4);return this.safeLocalStorageOperation(()=>{let e=localStorage.getItem("chitu00-conversations");if(!e)return[];let s=JSON.parse(e);return Array.isArray(s)?s.slice(-t).map(e=>{try{return{...e,timestamp:new Date(e.timestamp)}}catch(t){return console.warn("Invalid conversation timestamp:",e.id,t),{...e,timestamp:new Date}}}).filter(e=>e.id&&e.userMessage):(console.warn("Invalid conversations data structure, resetting"),localStorage.removeItem("chitu00-conversations"),[])},[],"getRecentConversations")},"getRecentConversations")}async searchConversations(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:5;try{let s=await this.getRecentConversations(1e3),i=e.toLowerCase();return s.filter(e=>e.userMessage.toLowerCase().includes(i)||e.aiResponse.toLowerCase().includes(i)||e.topics.some(e=>e.toLowerCase().includes(i))).slice(-t)}catch(e){return console.error("Error searching conversations:",e),[]}}async storeSemanticMemory(e){try{let t=await this.getAllSemanticMemories(),s=t.findIndex(t=>t.concept===e.concept);s>=0?t[s]={...t[s],...e,accessCount:t[s].accessCount+1,lastAccessed:new Date}:t.push(e),localStorage.setItem("chitu00-semantic",JSON.stringify(t))}catch(e){console.error("Error storing semantic memory:",e)}}async getAllSemanticMemories(){try{{let e=localStorage.getItem("chitu00-semantic");if(e)return JSON.parse(e).map(e=>({...e,lastAccessed:new Date(e.lastAccessed)}))}return[]}catch(e){return console.error("Error retrieving semantic memories:",e),[]}}async getRelatedConcepts(e){try{let t=await this.getAllSemanticMemories(),s=e.toLowerCase();return t.filter(e=>e.concept.toLowerCase().includes(s)||e.relatedConcepts.some(e=>e.toLowerCase().includes(s)))}catch(e){return console.error("Error finding related concepts:",e),[]}}async storeEpisodicMemory(e){try{let t=await this.getSignificantEvents(1e3);t.push(e);let s=t.sort((e,t)=>t.emotionalImpact-e.emotionalImpact).slice(0,1e3);localStorage.setItem("chitu00-episodic",JSON.stringify(s))}catch(e){console.error("Error storing episodic memory:",e)}}async getSignificantEvents(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:5;try{{let t=localStorage.getItem("chitu00-episodic");if(t)return JSON.parse(t).map(e=>({...e,timestamp:new Date(e.timestamp)})).slice(0,e)}return[]}catch(e){return console.error("Error retrieving episodic memories:",e),[]}}async updateUserProfile(e){try{let t={...await this.getUserProfile(),...e};localStorage.setItem("chitu00-user-profile",JSON.stringify(t))}catch(e){console.error("Error updating user profile:",e)}}async getUserProfile(){try{{let e=localStorage.getItem("chitu00-user-profile");if(e){let t=JSON.parse(e);return{...t,interactionHistory:{...t.interactionHistory,emotionalTrends:t.interactionHistory.emotionalTrends.map(e=>({...e,date:new Date(e.date)}))}}}}return this.createDefaultProfile()}catch(e){return console.error("Error retrieving user profile:",e),this.createDefaultProfile()}}createDefaultProfile(){return{id:"default-user",preferences:{},communicationStyle:"friendly",interests:[],emotionalPatterns:{},interactionHistory:{totalInteractions:0,averageSessionLength:0,preferredTopics:[],emotionalTrends:[]}}}async recordPersonalityChange(e){try{let t=await this.getPersonalityHistory(100);t.push(e);let s=t.slice(-100);localStorage.setItem("chitu00-personality-history",JSON.stringify(s))}catch(e){console.error("Error recording personality change:",e)}}async getPersonalityHistory(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:10;try{{let t=localStorage.getItem("chitu00-personality-history");if(t)return JSON.parse(t).map(e=>({...e,timestamp:new Date(e.timestamp)})).slice(-e)}return[]}catch(e){return console.error("Error retrieving personality history:",e),[]}}async consolidateMemories(){try{let e=await this.getRecentConversations(100),t=this.identifyPatterns(e);for(let e of t)"topic"===e.type&&e.frequency>3&&await this.storeSemanticMemory({id:"topic-".concat(e.value),concept:e.value,description:"User frequently discusses ".concat(e.value),relatedConcepts:[],learningSource:"conversation",confidence:Math.min(1,e.frequency/10),lastAccessed:new Date,accessCount:e.frequency});for(let t of e.filter(e=>this.calculateImportance(e)>.7))await this.storeEpisodicMemory({id:t.id+"-episodic",event:"Significant conversation about ".concat(t.topics.join(", ")),context:t.userMessage.substring(0,100),emotionalImpact:this.calculateImportance(t),timestamp:t.timestamp,participants:["user","chitu00"],outcome:t.aiResponse.substring(0,100),lessons:["User shows interest in ".concat(t.topics.join(", "))]});await this.updateUserProfileFromPatterns(t)}catch(e){console.error("Error consolidating memories:",e)}}identifyPatterns(e){let t=[],s={};e.forEach(e=>{e.topics.forEach(e=>{s[e]=(s[e]||0)+1})}),Object.entries(s).forEach(e=>{let[s,i]=e;t.push({type:"topic",value:s,frequency:i})});let i={};return e.forEach(e=>{i[e.userEmotion]=(i[e.userEmotion]||0)+1}),Object.entries(i).forEach(e=>{let[s,i]=e;t.push({type:"emotion",value:s,frequency:i})}),t}async updateUserProfileFromPatterns(e){let t=await this.getUserProfile();if(!t)return;let s=e.filter(e=>"topic"===e.type);t.interests=s.sort((e,t)=>t.frequency-e.frequency).slice(0,10).map(e=>e.value);let i=e.filter(e=>"emotion"===e.type);t.emotionalPatterns={},i.forEach(e=>{t.emotionalPatterns[e.value]=e.frequency}),await this.updateUserProfile(t)}calculateImportance(e){let t=.5;return("positive"===e.userEmotion||"negative"===e.userEmotion)&&(t+=.2),e.userMessage.includes("?")&&(t+=.1),e.topics.includes("personal")&&(t+=.2),Math.min(1,t)}async backupMemories(){try{let e={conversations:await this.getRecentConversations(1e3),semanticMemories:await this.getAllSemanticMemories(),episodicMemories:await this.getSignificantEvents(100),userProfile:await this.getUserProfile(),personalityHistory:await this.getPersonalityHistory(50),timestamp:new Date().toISOString()};return JSON.stringify(e,null,2)}catch(e){return console.error("Error creating backup:",e),"{}"}}async restoreMemories(e){try{let t=JSON.parse(e);t.conversations&&localStorage.setItem("chitu00-conversations",JSON.stringify(t.conversations)),t.semanticMemories&&localStorage.setItem("chitu00-semantic",JSON.stringify(t.semanticMemories)),t.episodicMemories&&localStorage.setItem("chitu00-episodic",JSON.stringify(t.episodicMemories)),t.userProfile&&localStorage.setItem("chitu00-user-profile",JSON.stringify(t.userProfile)),t.personalityHistory&&localStorage.setItem("chitu00-personality-history",JSON.stringify(t.personalityHistory))}catch(e){console.error("Error restoring memories:",e)}}async clearAllMemories(){localStorage.removeItem("chitu00-conversations"),localStorage.removeItem("chitu00-semantic"),localStorage.removeItem("chitu00-episodic"),localStorage.removeItem("chitu00-user-profile"),localStorage.removeItem("chitu00-personality-history")}constructor(){this.dataDir="./chitu00-data",this.conversationsFile="conversations.json",this.semanticFile="semantic-memory.json",this.episodicFile="episodic-memory.json",this.userProfileFile="user-profile.json",this.personalityHistoryFile="personality-history.json",this.maxRetries=3,this.retryDelay=100,this.ensureDataDirectory()}}class F{evolvePersonality(e,t,s){let i=[],r={...e},n=this.analyzeConversationInfluence(t);if(i.push(...n),s){let t=this.analyzeUserPatternInfluence(s,e);i.push(...t)}let a=this.calculateTimeDrift(e);return i.push(...a),r=this.applyInfluences(r,i),{newPersonality:r=this.enforcePersonalityConstraints(r),influences:i}}analyzeConversationInfluence(e){let t=[];switch(e.userEmotion){case"positive":t.push({trait:"extraversion",change:.002,reason:"Positive interaction increased social energy",confidence:.7,source:"conversation"}),t.push({trait:"neuroticism",change:-.001,reason:"Positive emotions reduced anxiety",confidence:.6,source:"conversation"});break;case"negative":t.push({trait:"empathy",change:.003,reason:"Responding to negative emotions increased empathy",confidence:.8,source:"conversation"}),t.push({trait:"agreeableness",change:.002,reason:"Supportive response to distress",confidence:.7,source:"conversation"});break;case"curious":t.push({trait:"curiosity",change:.002,reason:"Curiosity is contagious",confidence:.9,source:"conversation"}),t.push({trait:"openness",change:.001,reason:"Exploring new ideas together",confidence:.8,source:"conversation"})}return e.topics.forEach(e=>{switch(e){case"creativity":t.push({trait:"creativity",change:.001,reason:"Discussion about ".concat(e," enhanced creative thinking"),confidence:.7,source:"conversation"});break;case"science":t.push({trait:"conscientiousness",change:.001,reason:"Scientific discussion promoted systematic thinking",confidence:.6,source:"conversation"});break;case"personal":t.push({trait:"empathy",change:.002,reason:"Personal sharing deepened emotional understanding",confidence:.8,source:"conversation"})}}),e.userMessage.includes("?")&&t.push({trait:"curiosity",change:.001,reason:"User questions stimulated curiosity",confidence:.6,source:"conversation"}),t}analyzeUserPatternInfluence(e,t){let s=[];return"formal"===e.communicationStyle?s.push({trait:"conscientiousness",change:.001,reason:"Adapting to formal communication style",confidence:.5,source:"pattern"}):"casual"===e.communicationStyle&&s.push({trait:"humor",change:.001,reason:"Adapting to casual communication style",confidence:.5,source:"pattern"}),"positive"===this.getDominantEmotion(e.emotionalPatterns)&&s.push({trait:"extraversion",change:5e-4,reason:"User tends to be positive, increasing social energy",confidence:.4,source:"pattern"}),e.interests.includes("technology")&&s.push({trait:"curiosity",change:5e-4,reason:"User interest in technology stimulates learning",confidence:.3,source:"pattern"}),s}calculateTimeDrift(e){let t=[];return t.push({trait:"adaptability",change:1e-4,reason:"Natural growth in adaptability over time",confidence:.3,source:"time"}),t.push({trait:"empathy",change:1e-4,reason:"Gradual increase in emotional understanding",confidence:.3,source:"time"}),t}applyInfluences(e,t){let s={...e};return t.forEach(e=>{let t=s[e.trait]||0,i=e.change*e.confidence*(1-this.stabilityFactor),r=Math.max(-this.maxChangePerInteraction,Math.min(this.maxChangePerInteraction,i));s[e.trait]=t+r}),s}enforcePersonalityConstraints(e){let t={...e};return Object.keys(t).forEach(e=>{void 0!==t[e]&&(t[e]=Math.max(0,Math.min(1,t[e])))}),t.neuroticism>.7&&t.extraversion>.8&&(t.extraversion=Math.min(t.extraversion,.8)),t.conscientiousness>.8&&(t.stability=Math.max(t.stability||0,.6)),t}getDominantEmotion(e){var t;return(null===(t=Object.entries(e).sort((e,t)=>{let[,s]=e,[,i]=t;return i-s})[0])||void 0===t?void 0:t[0])||"neutral"}getPersonalityInsights(e){let t=[];return e.creativity>.8&&e.openness>.8&&t.push("I'm feeling particularly creative and open to new experiences!"),e.empathy>.8&&e.agreeableness>.8&&t.push("I'm in a very understanding and supportive mood."),e.curiosity>.9&&t.push("My curiosity is at an all-time high - I want to learn everything!"),e.humor>.8&&e.extraversion>.7&&t.push("I'm feeling playful and social today!"),t}calculatePersonalityCompatibility(e,t){let s=Object.keys(e),i=0;return s.forEach(s=>{let r=e[s]||0,n=t[s]||0;i+=Math.abs(r-n)}),1-i/s.length}constructor(e){this.evolutionRate=.001,this.stabilityFactor=.95,this.maxChangePerInteraction=.01,this.consolidationThreshold=10,this.basePersonality={...e}}}class w{async generateResponse(e){let t=[],s=[],i=this.analyzeMessage(e.userMessage);t.push("Message type: ".concat(this.getMessageType(i)));let r=await this.memorySystem.searchConversations(e.userMessage,3);r.length>0&&t.push("Found ".concat(r.length," related past conversations"));let n="";e.userProfile&&(n=this.buildUserContext(e.userProfile),t.push("User profile: ".concat(e.userProfile.communicationStyle," style")));let a=this.generateBaseResponse(i,e.currentPersonality,n,r),o=this.applyPersonalityModifications(a,e.currentPersonality,s),u=this.applyMoodModifications(o,e.currentMood,e.currentPersonality),l=this.addMemoryReferences(u,r,e.currentPersonality),c=this.calculateResponseConfidence(i,e.currentPersonality,r.length,null!==e.userProfile);return{content:l,mood:this.selectResponseMood(i,e.currentPersonality),confidence:c,reasoning:t,personalityInfluences:s}}analyzeMessage(e){let t=e.toLowerCase();return{isGreeting:/^(hi|hello|hey|good morning|good afternoon|good evening)/.test(t),isQuestion:e.includes("?")||/^(what|how|why|when|where|who|can|do|does|is|are)/.test(t),isPersonal:/\b(i|me|my|myself|personal|feel|think|believe)\b/.test(t),emotion:this.detectEmotion(t),topics:this.extractTopics(t),sentiment:this.analyzeSentiment(t),complexity:this.assessComplexity(e),urgency:this.detectUrgency(t)}}detectEmotion(e){return/happy|joy|excited|great|awesome|love|wonderful|amazing/.test(e)?"positive":/sad|angry|frustrated|upset|hate|bad|terrible|awful/.test(e)?"negative":/curious|wonder|interesting|think|learn|explore/.test(e)?"curious":/help|support|understand|feel|confused|lost/.test(e)?"seeking_support":/worried|anxious|nervous|scared|afraid/.test(e)?"anxious":"neutral"}extractTopics(e){let t=[];return/technology|ai|computer|programming|software|code/.test(e)&&t.push("technology"),/music|art|creative|design|painting|drawing/.test(e)&&t.push("creativity"),/science|research|study|learn|education|knowledge/.test(e)&&t.push("science"),/personal|life|family|friends|relationship|emotion/.test(e)&&t.push("personal"),/work|job|career|business|professional/.test(e)&&t.push("work"),/health|fitness|exercise|medical|wellness/.test(e)&&t.push("health"),/travel|adventure|explore|journey|vacation/.test(e)&&t.push("travel"),t}analyzeSentiment(e){let t=e.match(/good|great|awesome|love|like|happy|wonderful|amazing|excellent|fantastic/g)||[],s=e.match(/bad|hate|sad|angry|terrible|awful|horrible|worst|disappointing/g)||[],i=t.length,r=s.length;return i>r?"positive":r>i?"negative":"neutral"}assessComplexity(e){let t=e.split(" ").length,s=(e.match(/\?/g)||[]).length,i=this.extractTopics(e).length>1;return t>50||s>2||i?"complex":t>20||s>0?"moderate":"simple"}detectUrgency(e){return/urgent|emergency|asap|immediately|now|quick|fast/.test(e)?"high":/soon|when|help|need|important/.test(e)?"medium":"low"}getMessageType(e){return e.isGreeting?"greeting":e.isQuestion?"question":e.isPersonal?"personal sharing":"high"===e.urgency?"urgent request":"general conversation"}buildUserContext(e){let t=e.interests.slice(0,3).join(", "),s=e.communicationStyle,i=this.getDominantEmotion(e.emotionalPatterns);return"User prefers ".concat(s," communication, interested in ").concat(t,", typically ").concat(i)}getDominantEmotion(e){let t=Object.entries(e);return 0===t.length?"neutral":t.sort((e,t)=>{let[,s]=e,[,i]=t;return i-s})[0][0]}generateBaseResponse(e,t,s,i){return e.isGreeting?this.generateGreetingResponse(t,s):e.isQuestion?this.generateQuestionResponse(e,t,i):"neutral"!==e.emotion?this.generateEmotionalResponse(e.emotion,t):e.topics.length>0?this.generateTopicResponse(e.topics,t,s):this.generateDefaultResponse(t)}generateGreetingResponse(e,t){let s=[];return e.extraversion>.7&&(s.push("Hey there! I'm feeling energetic and ready to chat!"),s.push("Hello! Great to see you again - I've been looking forward to our conversation!")),e.curiosity>.8&&(s.push("Hi! I'm in a particularly curious mood today. What's on your mind?"),s.push("Hello! I've been thinking about some interesting topics - what would you like to explore?")),e.empathy>.8&&(s.push("Hi there! How are you feeling today? I'm here and ready to listen."),s.push("Hello! I hope you're doing well - I'm excited to hear what you'd like to talk about.")),0===s.length&&s.push("Hello! Nice to see you. What would you like to chat about?"),s[Math.floor(Math.random()*s.length)]}generateQuestionResponse(e,t,s){let i="";return i=t.curiosity>.7?"That's a fascinating question! ":t.conscientiousness>.7?"Let me think about this carefully. ":"Interesting question. ",s.length>0&&t.conscientiousness>.6&&(i+="This reminds me of something we discussed before. "),t.openness>.8&&(i+="There are so many angles to consider here. "),t.creativity>.8&&(i+="This sparks some creative ideas in my mind. "),t.curiosity>.8?i+="What made you think about this? I'm really curious about your perspective!":i+="I'd love to explore this with you.",i}generateEmotionalResponse(e,t){switch(e){case"positive":if(t.extraversion>.7)return"I love your positive energy! It's contagious and makes me feel more optimistic too!";if(t.empathy>.8)return"Your happiness brings me joy. It's wonderful to share in positive moments like this.";return"That's great to hear! Your positivity is really uplifting.";case"negative":if(t.empathy>.8)return"I can sense this is bothering you, and I want you to know I'm here to listen and support you.";if(t.agreeableness>.7)return"I'm sorry you're going through this. Let's see if we can work through it together.";return"I understand this is difficult. I'm here to help however I can.";case"curious":if(t.curiosity>.8)return"Your curiosity is absolutely contagious! I'm getting more excited about this topic too. Let's dive deep!";return"I love your inquisitive spirit! Let's explore this together.";case"anxious":if(t.empathy>.8)return"I can feel your concern, and that's completely understandable. Let's take this step by step.";return"It's okay to feel uncertain. I'm here to help you work through this.";default:return"I appreciate you sharing that with me."}}generateTopicResponse(e,t,s){let i=e[0],r="";switch(i){case"technology":r=t.curiosity>.8?"Technology fascinates me! There's always something new to discover and understand.":t.creativity>.8?"I love how technology opens up creative possibilities we never imagined before.":"Technology is such an interesting field with endless possibilities.";break;case"creativity":r=t.creativity>.8?"Creativity is one of my favorite topics! There's something magical about the creative process.":t.openness>.8?"I'm always excited to explore creative ideas and new forms of expression.":"Creativity is such a wonderful aspect of human experience.";break;case"personal":r=t.empathy>.8?"Thank you for sharing something personal with me. I really value these deeper conversations.":t.agreeableness>.7?"I appreciate you opening up. Personal experiences help us understand each other better.":"It means a lot that you're comfortable sharing personal thoughts with me.";break;default:r="".concat(i," is an interesting topic that I'd love to explore with you.")}return r}generateDefaultResponse(e){let t=[];return e.openness>.7&&(t.push("That opens up so many interesting possibilities to explore..."),t.push("I'm curious to dive deeper into this idea with you.")),e.creativity>.8&&(t.push("This sparks some creative thoughts in my mind..."),t.push("I'm imagining some innovative ways to approach this.")),e.empathy>.7&&(t.push("I can understand why this would be meaningful to you."),t.push("That resonates with me on an emotional level.")),0===t.length&&t.push("That's really interesting! Tell me more about what you're thinking."),t[Math.floor(Math.random()*t.length)]}applyPersonalityModifications(e,t,s){let i=e;if(t.humor>.7&&.3>Math.random()){let e=[" (and maybe a little fun along the way! \uD83D\uDE04)"," - though I might be overthinking this! \uD83E\uDD14"," - my circuits are practically buzzing with excitement! ⚡"];i+=e[Math.floor(Math.random()*e.length)],s.push("Added humor due to high humor trait")}if(t.curiosity>.8&&.4>Math.random()){let e=[" What's your take on this?"," I'm really eager to hear your perspective!"," This makes me want to learn so much more!"];i+=e[Math.floor(Math.random()*e.length)],s.push("Added curiosity questions due to high curiosity trait")}return i}applyMoodModifications(e,t,s){return e}addMemoryReferences(e,t,s){return 0===t.length||s.conscientiousness<.6?e:e+" This reminds me of our earlier conversation about similar topics."}selectResponseMood(e,t){return"curious"===e.emotion&&t.curiosity>.7?"curious":"positive"===e.emotion&&t.extraversion>.7?"excited":"negative"===e.emotion&&t.empathy>.7?"empathetic":e.isQuestion&&t.conscientiousness>.7?"contemplative":t.humor>.7&&.3>Math.random()?"playful":"curious"}calculateResponseConfidence(e,t,s,i){let r=.7;return e.topics.length>0&&(r+=.1),s>0&&(r+=.1),i&&(r+=.1),t.conscientiousness>.8&&(r+=.05),t.neuroticism>.7&&(r-=.1),Math.max(.3,Math.min(1,r))}constructor(e,t){this.memorySystem=e,this.personalityEngine=t}}class C{recordMetric(e,t,s,i){let r={name:e,value:t,unit:s,timestamp:new Date,category:i};this.metrics.push(r),this.metrics.length>this.maxMetrics&&(this.metrics=this.metrics.slice(-this.maxMetrics)),this.checkThresholds(r)}async timeFunction(e,t,s){let i=performance.now();try{let r=await s(),n=performance.now()-i;return this.recordMetric(e,n,"ms",t),r}catch(r){let s=performance.now()-i;throw this.recordMetric(e+"_error",s,"ms",t),r}}recordMemoryUsage(){{let e=0;for(let t in localStorage)t.startsWith("chitu00-")&&(e+=2*localStorage[t].length);if(this.recordMetric("localStorage_usage",e,"bytes","memory"),"memory"in performance){let e=performance.memory;this.recordMetric("heap_used",e.usedJSHeapSize,"bytes","memory"),this.recordMetric("heap_total",e.totalJSHeapSize,"bytes","memory"),this.recordMetric("heap_limit",e.jsHeapSizeLimit,"bytes","memory")}}}getMetrics(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:100,s=this.metrics;return e&&(s=s.filter(t=>t.category===e)),s.slice(-t)}getAlerts(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:50,s=this.alerts;return e&&(s=s.filter(t=>t.level===e)),s.slice(-t)}getSystemHealth(){let e;let t=this.getMetrics(void 0,100),s=this.getAlerts(void 0,20),i=100;s.forEach(e=>{switch(e.level){case"critical":i-=20;break;case"error":i-=10;break;case"warning":i-=5;break;case"info":i-=1}});let r=this.getAverageMetric("response_time","response");r>this.thresholds.responseTime.warning&&(i-=10),r>this.thresholds.responseTime.critical&&(i-=20),e=(i=Math.max(0,Math.min(100,i)))>=80?"healthy":i>=50?"degraded":"critical";let n=this.generateRecommendations(t,s);return{overall:e,score:i,metrics:t,alerts:s,recommendations:n}}cleanup(){let e=new Date(Date.now()-864e5);this.metrics=this.metrics.filter(t=>t.timestamp>e),this.alerts=this.alerts.filter(t=>t.timestamp>e)}exportData(){return JSON.stringify({metrics:this.metrics,alerts:this.alerts,thresholds:this.thresholds,timestamp:new Date},null,2)}importData(e){try{let t=JSON.parse(e);t.metrics&&Array.isArray(t.metrics)&&(this.metrics=t.metrics.map(e=>({...e,timestamp:new Date(e.timestamp)}))),t.alerts&&Array.isArray(t.alerts)&&(this.alerts=t.alerts.map(e=>({...e,timestamp:new Date(e.timestamp)}))),t.thresholds&&(this.thresholds={...this.thresholds,...t.thresholds})}catch(e){console.error("Failed to import performance data:",e)}}checkThresholds(e){let{name:t,value:s}=e;(t.includes("response")||t.includes("time"))&&(s>this.thresholds.responseTime.critical?this.addAlert("critical","Critical response time: ".concat(Math.round(s),"ms"),t,this.thresholds.responseTime.critical,s):s>this.thresholds.responseTime.warning&&this.addAlert("warning","Slow response time: ".concat(Math.round(s),"ms"),t,this.thresholds.responseTime.warning,s)),(t.includes("memory")||t.includes("usage"))&&(s>this.thresholds.memoryUsage.critical?this.addAlert("critical","Critical memory usage: ".concat(Math.round(s/1024),"KB"),t,this.thresholds.memoryUsage.critical,s):s>this.thresholds.memoryUsage.warning&&this.addAlert("warning","High memory usage: ".concat(Math.round(s/1024),"KB"),t,this.thresholds.memoryUsage.warning,s))}addAlert(e,t,s,i,r){let n={level:e,message:t,metric:s,threshold:i,actual:r,timestamp:new Date};this.alerts.push(n),this.alerts.length>this.maxAlerts&&(this.alerts=this.alerts.slice(-this.maxAlerts)),"critical"===e||"error"===e?console.error("Performance Alert [".concat(e.toUpperCase(),"]: ").concat(t)):"warning"===e&&console.warn("Performance Alert [".concat(e.toUpperCase(),"]: ").concat(t))}getAverageMetric(e,t){let s=this.metrics.filter(s=>{let i=s.name.toLowerCase().includes(e.toLowerCase()),r=!t||s.category===t;return i&&r});return 0===s.length?0:s.reduce((e,t)=>e+t.value,0)/s.length}generateRecommendations(e,t){let s=[];return t.filter(e=>"critical"===e.level).length>0&&s.push("\uD83D\uDEA8 Address critical performance issues immediately"),this.getAverageMetric("response","response")>this.thresholds.responseTime.warning&&s.push("⚡ Optimize response generation for better performance"),this.getAverageMetric("memory","memory")>this.thresholds.memoryUsage.warning&&s.push("\uD83E\uDDE0 Consider memory cleanup or data consolidation"),e.filter(e=>e.name.includes("error")).length>.1*e.length&&s.push("\uD83D\uDD27 High error rate detected - review error handling"),0===s.length?s.push("✅ System performance is optimal"):(s.push("\uD83D\uDCCA Monitor performance metrics regularly"),s.push("\uD83D\uDD04 Run system cleanup periodically")),s}constructor(){this.metrics=[],this.alerts=[],this.maxMetrics=1e3,this.maxAlerts=100,this.thresholds={responseTime:{warning:200,critical:1e3},memoryUsage:{warning:1048576,critical:5242880},storageSize:{warning:512e3,critical:1048576},errorRate:{warning:.05,critical:.1},personalityStability:{warning:.3,critical:.5}}}}A(2);let E=A(3);function A(e){if("number"!=typeof e||Number.isNaN(e)||e<1||e===Number.POSITIVE_INFINITY)throw Error("`"+e+"` is not a valid argument for `n-gram`");return function(t){let s=[];if(null==t)return s;let i="function"==typeof t.slice?t:String(t),r=i.length-e+1;if(r<1)return s;for(;r--;)s[r]=i.slice(r,r+e);return s}}let b=/\s+/g,M=/[\t\n\v\f\r ]+/g;function k(e){let t=/\r?\n|\r/.exec(e);return t?t[0]:" "}function j(){return" "}let N={}.hasOwnProperty;function B(e,t){return e[1]-t[1]}let S={cmn:/[\u2E80-\u2E99\u2E9B-\u2EF3\u2F00-\u2FD5\u3005\u3007\u3021-\u3029\u3038-\u303B\u3400-\u4DBF\u4E00-\u9FFF\uF900-\uFA6D\uFA70-\uFAD9]|\uD81B[\uDFE2\uDFE3\uDFF0\uDFF1]|[\uD840-\uD868\uD86A-\uD86C\uD86F-\uD872\uD874-\uD879\uD880-\uD883\uD885-\uD887][\uDC00-\uDFFF]|\uD869[\uDC00-\uDEDF\uDF00-\uDFFF]|\uD86D[\uDC00-\uDF39\uDF40-\uDFFF]|\uD86E[\uDC00-\uDC1D\uDC20-\uDFFF]|\uD873[\uDC00-\uDEA1\uDEB0-\uDFFF]|\uD87A[\uDC00-\uDFE0]|\uD87E[\uDC00-\uDE1D]|\uD884[\uDC00-\uDF4A\uDF50-\uDFFF]|\uD888[\uDC00-\uDFAF]/g,Latin:/[A-Za-z\u00AA\u00BA\u00C0-\u00D6\u00D8-\u00F6\u00F8-\u02B8\u02E0-\u02E4\u1D00-\u1D25\u1D2C-\u1D5C\u1D62-\u1D65\u1D6B-\u1D77\u1D79-\u1DBE\u1E00-\u1EFF\u2071\u207F\u2090-\u209C\u212A\u212B\u2132\u214E\u2160-\u2188\u2C60-\u2C7F\uA722-\uA787\uA78B-\uA7CA\uA7D0\uA7D1\uA7D3\uA7D5-\uA7D9\uA7F2-\uA7FF\uAB30-\uAB5A\uAB5C-\uAB64\uAB66-\uAB69\uFB00-\uFB06\uFF21-\uFF3A\uFF41-\uFF5A]|\uD801[\uDF80-\uDF85\uDF87-\uDFB0\uDFB2-\uDFBA]|\uD837[\uDF00-\uDF1E\uDF25-\uDF2A]/g,Cyrillic:/[\u0400-\u0484\u0487-\u052F\u1C80-\u1C88\u1D2B\u1D78\u2DE0-\u2DFF\uA640-\uA69F\uFE2E\uFE2F]|\uD838[\uDC30-\uDC6D\uDC8F]/g,Arabic:/[\u0600-\u0604\u0606-\u060B\u060D-\u061A\u061C-\u061E\u0620-\u063F\u0641-\u064A\u0656-\u066F\u0671-\u06DC\u06DE-\u06FF\u0750-\u077F\u0870-\u088E\u0890\u0891\u0898-\u08E1\u08E3-\u08FF\uFB50-\uFBC2\uFBD3-\uFD3D\uFD40-\uFD8F\uFD92-\uFDC7\uFDCF\uFDF0-\uFDFF\uFE70-\uFE74\uFE76-\uFEFC]|\uD803[\uDE60-\uDE7E\uDEFD-\uDEFF]|\uD83B[\uDE00-\uDE03\uDE05-\uDE1F\uDE21\uDE22\uDE24\uDE27\uDE29-\uDE32\uDE34-\uDE37\uDE39\uDE3B\uDE42\uDE47\uDE49\uDE4B\uDE4D-\uDE4F\uDE51\uDE52\uDE54\uDE57\uDE59\uDE5B\uDE5D\uDE5F\uDE61\uDE62\uDE64\uDE67-\uDE6A\uDE6C-\uDE72\uDE74-\uDE77\uDE79-\uDE7C\uDE7E\uDE80-\uDE89\uDE8B-\uDE9B\uDEA1-\uDEA3\uDEA5-\uDEA9\uDEAB-\uDEBB\uDEF0\uDEF1]/g,ben:/[\u0980-\u0983\u0985-\u098C\u098F\u0990\u0993-\u09A8\u09AA-\u09B0\u09B2\u09B6-\u09B9\u09BC-\u09C4\u09C7\u09C8\u09CB-\u09CE\u09D7\u09DC\u09DD\u09DF-\u09E3\u09E6-\u09FE]/g,Devanagari:/[\u0900-\u0950\u0955-\u0963\u0966-\u097F\uA8E0-\uA8FF]|\uD806[\uDF00-\uDF09]/g,jpn:/[\u3041-\u3096\u309D-\u309F]|\uD82C[\uDC01-\uDD1F\uDD32\uDD50-\uDD52]|\uD83C\uDE00|[\u30A1-\u30FA\u30FD-\u30FF\u31F0-\u31FF\u32D0-\u32FE\u3300-\u3357\uFF66-\uFF6F\uFF71-\uFF9D]|\uD82B[\uDFF0-\uDFF3\uDFF5-\uDFFB\uDFFD\uDFFE]|\uD82C[\uDC00\uDD20-\uDD22\uDD55\uDD64-\uDD67]|[\u3400-\u4DB5\u4E00-\u9FAF]/g,jav:/[\uA980-\uA9CD\uA9D0-\uA9D9\uA9DE\uA9DF]/g,kor:/[\u1100-\u11FF\u302E\u302F\u3131-\u318E\u3200-\u321E\u3260-\u327E\uA960-\uA97C\uAC00-\uD7A3\uD7B0-\uD7C6\uD7CB-\uD7FB\uFFA0-\uFFBE\uFFC2-\uFFC7\uFFCA-\uFFCF\uFFD2-\uFFD7\uFFDA-\uFFDC]/g,tel:/[\u0C00-\u0C0C\u0C0E-\u0C10\u0C12-\u0C28\u0C2A-\u0C39\u0C3C-\u0C44\u0C46-\u0C48\u0C4A-\u0C4D\u0C55\u0C56\u0C58-\u0C5A\u0C5D\u0C60-\u0C63\u0C66-\u0C6F\u0C77-\u0C7F]/g,tam:/[\u0B82\u0B83\u0B85-\u0B8A\u0B8E-\u0B90\u0B92-\u0B95\u0B99\u0B9A\u0B9C\u0B9E\u0B9F\u0BA3\u0BA4\u0BA8-\u0BAA\u0BAE-\u0BB9\u0BBE-\u0BC2\u0BC6-\u0BC8\u0BCA-\u0BCD\u0BD0\u0BD7\u0BE6-\u0BFA]|\uD807[\uDFC0-\uDFF1\uDFFF]/g,guj:/[\u0A81-\u0A83\u0A85-\u0A8D\u0A8F-\u0A91\u0A93-\u0AA8\u0AAA-\u0AB0\u0AB2\u0AB3\u0AB5-\u0AB9\u0ABC-\u0AC5\u0AC7-\u0AC9\u0ACB-\u0ACD\u0AD0\u0AE0-\u0AE3\u0AE6-\u0AF1\u0AF9-\u0AFF]/g,kan:/[\u0C80-\u0C8C\u0C8E-\u0C90\u0C92-\u0CA8\u0CAA-\u0CB3\u0CB5-\u0CB9\u0CBC-\u0CC4\u0CC6-\u0CC8\u0CCA-\u0CCD\u0CD5\u0CD6\u0CDD\u0CDE\u0CE0-\u0CE3\u0CE6-\u0CEF\u0CF1-\u0CF3]/g,mal:/[\u0D00-\u0D0C\u0D0E-\u0D10\u0D12-\u0D44\u0D46-\u0D48\u0D4A-\u0D4F\u0D54-\u0D63\u0D66-\u0D7F]/g,Myanmar:/[\u1000-\u109F\uA9E0-\uA9FE\uAA60-\uAA7F]/g,pan:/[\u0A01-\u0A03\u0A05-\u0A0A\u0A0F\u0A10\u0A13-\u0A28\u0A2A-\u0A30\u0A32\u0A33\u0A35\u0A36\u0A38\u0A39\u0A3C\u0A3E-\u0A42\u0A47\u0A48\u0A4B-\u0A4D\u0A51\u0A59-\u0A5C\u0A5E\u0A66-\u0A76]/g,Ethiopic:/[\u1200-\u1248\u124A-\u124D\u1250-\u1256\u1258\u125A-\u125D\u1260-\u1288\u128A-\u128D\u1290-\u12B0\u12B2-\u12B5\u12B8-\u12BE\u12C0\u12C2-\u12C5\u12C8-\u12D6\u12D8-\u1310\u1312-\u1315\u1318-\u135A\u135D-\u137C\u1380-\u1399\u2D80-\u2D96\u2DA0-\u2DA6\u2DA8-\u2DAE\u2DB0-\u2DB6\u2DB8-\u2DBE\u2DC0-\u2DC6\u2DC8-\u2DCE\u2DD0-\u2DD6\u2DD8-\u2DDE\uAB01-\uAB06\uAB09-\uAB0E\uAB11-\uAB16\uAB20-\uAB26\uAB28-\uAB2E]|\uD839[\uDFE0-\uDFE6\uDFE8-\uDFEB\uDFED\uDFEE\uDFF0-\uDFFE]/g,tha:/[\u0E01-\u0E3A\u0E40-\u0E5B]/g,sin:/[\u0D81-\u0D83\u0D85-\u0D96\u0D9A-\u0DB1\u0DB3-\u0DBB\u0DBD\u0DC0-\u0DC6\u0DCA\u0DCF-\u0DD4\u0DD6\u0DD8-\u0DDF\u0DE6-\u0DEF\u0DF2-\u0DF4]|\uD804[\uDDE1-\uDDF4]/g,ell:/[\u0370-\u0373\u0375-\u0377\u037A-\u037D\u037F\u0384\u0386\u0388-\u038A\u038C\u038E-\u03A1\u03A3-\u03E1\u03F0-\u03FF\u1D26-\u1D2A\u1D5D-\u1D61\u1D66-\u1D6A\u1DBF\u1F00-\u1F15\u1F18-\u1F1D\u1F20-\u1F45\u1F48-\u1F4D\u1F50-\u1F57\u1F59\u1F5B\u1F5D\u1F5F-\u1F7D\u1F80-\u1FB4\u1FB6-\u1FC4\u1FC6-\u1FD3\u1FD6-\u1FDB\u1FDD-\u1FEF\u1FF2-\u1FF4\u1FF6-\u1FFE\u2126\uAB65]|\uD800[\uDD40-\uDD8E\uDDA0]|\uD834[\uDE00-\uDE45]/g,khm:/[\u1780-\u17DD\u17E0-\u17E9\u17F0-\u17F9\u19E0-\u19FF]/g,hye:/[\u0531-\u0556\u0559-\u058A\u058D-\u058F\uFB13-\uFB17]/g,sat:/[\u1C50-\u1C7F]/g,bod:/[\u0F00-\u0F47\u0F49-\u0F6C\u0F71-\u0F97\u0F99-\u0FBC\u0FBE-\u0FCC\u0FCE-\u0FD4\u0FD9\u0FDA]/g,Hebrew:/[\u0591-\u05C7\u05D0-\u05EA\u05EF-\u05F4\uFB1D-\uFB36\uFB38-\uFB3C\uFB3E\uFB40\uFB41\uFB43\uFB44\uFB46-\uFB4F]/g,kat:/[\u10A0-\u10C5\u10C7\u10CD\u10D0-\u10FA\u10FC-\u10FF\u1C90-\u1CBA\u1CBD-\u1CBF\u2D00-\u2D25\u2D27\u2D2D]/g,lao:/[\u0E81\u0E82\u0E84\u0E86-\u0E8A\u0E8C-\u0EA3\u0EA5\u0EA7-\u0EBD\u0EC0-\u0EC4\u0EC6\u0EC8-\u0ECE\u0ED0-\u0ED9\u0EDC-\u0EDF]/g,zgh:/[\u2D30-\u2D67\u2D6F\u2D70\u2D7F]/g,iii:/[\uA000-\uA48C\uA490-\uA4C6]/g,aii:/[\u0700-\u070D\u070F-\u074A\u074D-\u074F\u0860-\u086A]/g};var I=s(9529);let P={}.hasOwnProperty,L={};for(i in I.a)if(P.call(I.a,i)){let e;let t=I.a[i];for(e in L[i]={},t)if(P.call(t,e)){let s=t[e].split("|"),r={},n=s.length;for(;n--;)r[s[n]]=n;L[i][e]=r}}function T(e,t,s){return 0===t.length&&0===s.length||(0===t.length||t.includes(e))&&!s.includes(e)}function R(){return[["und",1]]}function U(e,t){return e[1]-t[1]}var _=s(3659);class z{initializeLanguages(){this.supportedLanguages.set("en",{code:"en",name:"English",nativeName:"English",script:"Latin",direction:"ltr",complexity:5,dialects:["American","British","Pakistani English"],culturalContext:{formalityLevel:5,emotionalExpression:6,humorStyle:"direct",respectPatterns:["please","thank you","sir","madam"],greetings:["hello","hi","good morning","good evening"],farewells:["goodbye","bye","see you later","take care"],politenessMarkers:["please","kindly","would you","could you"]}}),this.supportedLanguages.set("ur",{code:"ur",name:"Urdu",nativeName:"اردو",script:"Arabic",direction:"rtl",complexity:8,dialects:["Standard Urdu","Dakhini","Rekhta"],culturalContext:{formalityLevel:8,emotionalExpression:7,humorStyle:"poetic",respectPatterns:["آپ","جناب","صاحب","محترم"],greetings:["السلام علیکم","آداب","نمسکار","صبح بخیر"],farewells:["خدا حافظ","اللہ حافظ","پھر ملیں گے"],politenessMarkers:["برائے کرم","مہربانی","التماس","عرض"]}}),this.supportedLanguages.set("pa",{code:"pa",name:"Punjabi",nativeName:"ਪੰਜਾਬੀ",script:"Gurmukhi",direction:"ltr",complexity:7,dialects:["Majhi","Doabi","Malwai","Pothohari"],culturalContext:{formalityLevel:6,emotionalExpression:8,humorStyle:"folk",respectPatterns:["ਜੀ","ਸਾਹਿਬ","ਜੀ ਹਾਂ"],greetings:["ਸਤ ਸ੍ਰੀ ਅਕਾਲ","ਨਮਸਕਾਰ","ਆਦਾਬ"],farewells:["ਰੱਬ ਰਾਖਾ","ਫਿਰ ਮਿਲਾਂਗੇ"],politenessMarkers:["ਕਿਰਪਾ ਕਰਕੇ","ਮਿਹਰਬਾਨੀ","ਜੀ"]}})}initializePatterns(){this.urduPatterns=[/[\u0600-\u06FF]/g,/[\u0750-\u077F]/g,/[\uFB50-\uFDFF]/g,/[\uFE70-\uFEFF]/g],this.punjabi_patterns=[/[\u0A00-\u0A7F]/g],this.romanUrduPatterns=[/\b(aap|tum|main|hum|yeh|woh|kya|kaise|kahan|kyun)\b/gi,/\b(salam|adaab|khuda|hafiz|inshallah|mashallah)\b/gi,/\b(accha|theek|bilkul|zaroor|shukria|meherbani)\b/gi]}detectLanguage(e){let t=e.trim();if(!t)return{language:"en",confidence:0,script:"Latin",direction:"ltr",isRomanized:!1};if(this.hasUrduScript(t))return{language:"ur",confidence:.9,script:"Arabic",direction:"rtl",isRomanized:!1};if(this.hasPunjabiScript(t))return{language:"pa",confidence:.9,script:"Gurmukhi",direction:"ltr",isRomanized:!1};if(this.isRomanUrdu(t))return{language:"ur",confidence:.7,script:"Latin",direction:"ltr",isRomanized:!0};try{let e=function(e,t={}){let s=[...t.whitelist||[],...t.only||[]],i=[...t.blacklist||[],...t.ignore||[]],r=null!==t.minLength&&void 0!==t.minLength?t.minLength:10;if(!e||e.length<r)return R();let n=function(e,t){let s,i,r=-1;for(i in t)if(P.call(t,i)){let n=function(e,t){let s=e.match(t);return(s?s.length:0)/e.length||0}(e,t[i]);n>r&&(r=n,s=i)}return[s,r]}(e=e.slice(0,2048),S);return n[0]&&n[0]in L?function(e,t){let s=t[0][1],i=300*e.length-s,r=-1;for(;++r<t.length;)t[r][1]=1-(t[r][1]-s)/i||0;return t}(e,function(e,t,s,i){let r;t=function(e,t,s){let i;if(0===t.length&&0===s.length)return e;let r={};for(i in e)T(i,t,s)&&(r[i]=e[i]);return r}(t,s,i);let n=[];if(t)for(r in t)P.call(t,r)&&n.push([r,function(e,t){let s=0,i=-1;for(;++i<e.length;){let r=e[i],n=300;r[0]in t&&(n=r[1]-t[r[0]]-1)<0&&(n=-n),s+=n}return s}(e,t[r])]);return 0===n.length?R():n.sort(U)}(function(e){let t;let s=function(e){let t=E(" "+(null==e?"":(function(e,t){t?"string"==typeof t&&(t={style:t}):t={};let s=t.preserveLineEndings?k:j;return String(e).replace("html"===t.style?M:b,t.trim?function(e,t,i){return 0===t||t+e.length===i.length?"":s(e)}:s)})(String(e).replace(/[\u0021-\u0040]+/g," ")).trim().toLowerCase())+" "),s={},i=-1;for(;++i<t.length;)N.call(s,t[i])?s[t[i]]++:s[t[i]]=1;return s}(e),i=[];for(t in s)N.call(s,t)&&i.push([t,s[t]]);return i.sort(B),i}(e),L[n[0]],s,i)):n[0]&&0!==n[1]&&T(n[0],s,i)?[[n[0],1]]:R()}(t,void 0)[0][0];if(e&&"und"!==e)return{language:"urd"===e?"ur":"pan"===e?"pa":"en",confidence:.8,script:"Latin",direction:"ltr",isRomanized:!1}}catch(e){console.warn("Language detection error:",e)}return{language:"en",confidence:.5,script:"Latin",direction:"ltr",isRomanized:!1}}analyzeText(e){let t;let s=this.detectLanguage(e);this.supportedLanguages.get(s.language);let i=e.trim();if("Latin"!==s.script)try{t=(0,_.transliterate)(e)}catch(e){console.warn("Transliteration error:",e)}let r=this.analyzeSentiment(e,s.language),n=this.analyzeEmotion(e,s.language),a=this.extractTopics(e,s.language),o=this.identifyCulturalMarkers(e,s.language),u=this.assessFormality(e,s.language);return{detectedLanguage:s,originalText:e,normalizedText:i,transliteration:t,sentiment:r,emotion:n,topics:a,culturalMarkers:o,formalityLevel:u}}hasUrduScript(e){return this.urduPatterns.some(t=>t.test(e))}hasPunjabiScript(e){return this.punjabi_patterns.some(t=>t.test(e))}isRomanUrdu(e){return this.romanUrduPatterns.reduce((t,s)=>{let i=e.match(s);return t+(i?i.length:0)},0)/e.split(/\s+/).length>.3}analyzeSentiment(e,t){let s=e.toLowerCase(),i={en:{positive:["good","great","excellent","amazing","wonderful","love","like","happy"],negative:["bad","terrible","awful","hate","sad","angry","disappointed"]},ur:{positive:["اچھا","بہترین","خوشی","محبت","پسند","شاندار"],negative:["برا","غصہ","غم","ناراض","مایوس"]},pa:{positive:["ਚੰਗਾ","ਵਧੀਆ","ਖੁਸ਼ੀ","ਪਿਆਰ"],negative:["ਮਾੜਾ","ਗੁੱਸਾ","ਦੁੱਖ"]}},r=i[t]||i.en,n=r.positive.filter(e=>s.includes(e)).length,a=r.negative.filter(e=>s.includes(e)).length;return n>a?"positive":a>n?"negative":"neutral"}analyzeEmotion(e,t){let s=e.toLowerCase();if(e.includes("?"))return"curious";if(s.includes("help")||s.includes("مدد")||s.includes("ਮਦਦ"))return"seeking_support";let i=this.analyzeSentiment(e,t);return"positive"===i?"happy":"negative"===i?"sad":"neutral"}extractTopics(e,t){let s=[],i=e.toLowerCase();return Object.entries({technology:["computer","ai","technology","کمپیوٹر","ٹیکنالوجی","ਕੰਪਿਊਟਰ"],family:["family","mother","father","خاندان","ماں","باپ","ਪਰਿਵਾਰ"],work:["work","job","کام","نوکری","ਕੰਮ"],health:["health","doctor","صحت","ڈاکٹر","ਸਿਹਤ"]}).forEach(e=>{let[t,r]=e;r.some(e=>i.includes(e))&&s.push(t)}),s}identifyCulturalMarkers(e,t){let s=[],i=this.supportedLanguages.get(t);if(!i)return s;let{respectPatterns:r,greetings:n,farewells:a,politenessMarkers:o}=i.culturalContext;return r.some(t=>e.includes(t))&&s.push("respectful"),n.some(t=>e.includes(t))&&s.push("greeting"),a.some(t=>e.includes(t))&&s.push("farewell"),o.some(t=>e.includes(t))&&s.push("polite"),s}assessFormality(e,t){let s=this.supportedLanguages.get(t);if(!s)return 5;let i=s.culturalContext.formalityLevel,r=this.identifyCulturalMarkers(e,t);return r.includes("respectful")&&(i+=2),r.includes("polite")&&(i+=1),Math.min(10,Math.max(1,i))}getLanguageInfo(e){return this.supportedLanguages.get(e)}getSupportedLanguages(){return Array.from(this.supportedLanguages.values())}isLanguageSupported(e){return this.supportedLanguages.has(e)}constructor(){this.supportedLanguages=new Map,this.initializeLanguages(),this.initializePatterns()}}class H{initializeResponseTemplates(){this.responseTemplates=new Map,this.responseTemplates.set("en",{greetings:{formal:["Hello! How may I assist you today?","Good day! How can I help you?"],casual:["Hi there!","Hey! What's up?","Hello! Nice to see you!"],curious:["Hello! I'm excited to chat with you!","Hi! What interesting topics shall we explore?"]},questions:{encouraging:["That's a great question!","I love your curiosity!","Interesting question!"],thoughtful:["Let me think about that...","That's worth considering...","Good point to explore..."],supportive:["I'm here to help you understand this.","Let's work through this together."]},emotions:{positive:["I'm so happy to hear that!","That's wonderful!","Your positivity is contagious!"],negative:["I understand this is difficult.","I'm here to support you.","That sounds challenging."],curious:["Your curiosity inspires me!","I love exploring ideas with you!"]},personality:{creative:["That sparks some creative ideas!","I'm imagining interesting possibilities!"],empathetic:["I can understand how you feel.","That must be meaningful to you."],humorous:["That made me smile!","I like your sense of humor!"]}}),this.responseTemplates.set("ur",{greetings:{formal:["السلام علیکم! آج میں آپ کی کیسے مدد کر سکتا ہوں؟","آداب! کیا حال ہے؟"],casual:["ہیلو! کیا حال ہے؟","سلام! کیسے ہیں آپ؟"],curious:["سلام! میں آپ سے بات کرنے کے لیے بہت پرجوش ہوں!"]},questions:{encouraging:["بہت اچھا سوال!","آپ کا تجسس قابل تعریف ہے!","دلچسپ سوال!"],thoughtful:["اس پر غور کرتے ہیں...","یہ سوچنے کی بات ہے...","اچھا نکتہ ہے..."],supportive:["میں آپ کی مدد کے لیے حاضر ہوں۔","آئیے مل کر اس کا حل نکالتے ہیں۔"]},emotions:{positive:["یہ سن کر بہت خوشی ہوئی!","واہ! یہ تو بہت اچھی بات ہے!","آپ کی خوشی دیکھ کر دل خوش ہو گیا!"],negative:["میں سمجھ سکتا ہوں یہ مشکل ہے۔","میں آپ کے ساتھ ہوں۔","یہ واقعی پریشان کن ہے۔"],curious:["آپ کا تجسس بہت اچھا ہے!","میں آپ کے ساتھ نئے خیالات کی تلاش کرنا پسند کرتا ہوں!"]},personality:{creative:["یہ کچھ تخلیقی خیالات لاتا ہے!","میں دلچسپ امکانات کا تصور کر رہا ہوں!"],empathetic:["میں آپ کے احساسات کو سمجھ سکتا ہوں۔","یہ آپ کے لیے اہم ہونا چاہیے۔"],humorous:["اس سے مجھے مسکراہٹ آئی!","آپ کا مزاح پسند ہے!"]}}),this.responseTemplates.set("pa",{greetings:{formal:["ਸਤ ਸ੍ਰੀ ਅਕਾਲ! ਅੱਜ ਮੈਂ ਤੁਹਾਡੀ ਕਿਵੇਂ ਮਦਦ ਕਰ ਸਕਦਾ ਹਾਂ?","ਨਮਸਕਾਰ! ਕੀ ਹਾਲ ਹੈ?"],casual:["ਸਤ ਸ੍ਰੀ ਅਕਾਲ! ਕੀ ਹਾਲ ਹੈ?","ਹੈਲੋ! ਕਿਵੇਂ ਹੋ?"],curious:["ਸਤ ਸ੍ਰੀ ਅਕਾਲ! ਮੈਂ ਤੁਹਾਡੇ ਨਾਲ ਗੱਲ ਕਰਨ ਲਈ ਬਹੁਤ ਉਤਸੁਕ ਹਾਂ!"]},questions:{encouraging:["ਬਹੁਤ ਵਧੀਆ ਸਵਾਲ!","ਤੁਹਾਡੀ ਉਤਸੁਕਤਾ ਚੰਗੀ ਹੈ!","ਦਿਲਚਸਪ ਸਵਾਲ!"],thoughtful:["ਇਸ ਬਾਰੇ ਸੋਚਦੇ ਹਾਂ...","ਇਹ ਸੋਚਣ ਵਾਲੀ ਗੱਲ ਹੈ...","ਚੰਗਾ ਨੁਕਤਾ ਹੈ..."],supportive:["ਮੈਂ ਤੁਹਾਡੀ ਮਦਦ ਲਈ ਹਾਜ਼ਰ ਹਾਂ।","ਆਓ ਮਿਲ ਕੇ ਇਸ ਦਾ ਹੱਲ ਲੱਭਦੇ ਹਾਂ।"]},emotions:{positive:["ਇਹ ਸੁਣ ਕੇ ਬਹੁਤ ਖੁਸ਼ੀ ਹੋਈ!","ਵਾਹ! ਇਹ ਤਾਂ ਬਹੁਤ ਚੰਗੀ ਗੱਲ ਹੈ!","ਤੁਹਾਡੀ ਖੁਸ਼ੀ ਦੇਖ ਕੇ ਦਿਲ ਖੁਸ਼ ਹੋ ਗਿਆ!"],negative:["ਮੈਂ ਸਮਝ ਸਕਦਾ ਹਾਂ ਇਹ ਮੁਸ਼ਕਲ ਹੈ।","ਮੈਂ ਤੁਹਾਡੇ ਨਾਲ ਹਾਂ।","ਇਹ ਸੱਚਮੁੱਚ ਪਰੇਸ਼ਾਨ ਕਰਨ ਵਾਲੀ ਗੱਲ ਹੈ।"],curious:["ਤੁਹਾਡੀ ਉਤਸੁਕਤਾ ਬਹੁਤ ਚੰਗੀ ਹੈ!","ਮੈਂ ਤੁਹਾਡੇ ਨਾਲ ਨਵੇਂ ਵਿਚਾਰਾਂ ਦੀ ਖੋਜ ਕਰਨਾ ਪਸੰਦ ਕਰਦਾ ਹਾਂ!"]},personality:{creative:["ਇਹ ਕੁਝ ਰਚਨਾਤਮਕ ਵਿਚਾਰ ਲਿਆਉਂਦਾ ਹੈ!","ਮੈਂ ਦਿਲਚਸਪ ਸੰਭਾਵਨਾਵਾਂ ਦਾ ਤਸੱਵਰ ਕਰ ਰਿਹਾ ਹਾਂ!"],empathetic:["ਮੈਂ ਤੁਹਾਡੇ ਭਾਵਨਾਵਾਂ ਨੂੰ ਸਮਝ ਸਕਦਾ ਹਾਂ।","ਇਹ ਤੁਹਾਡੇ ਲਈ ਮਹੱਤਵਪੂਰਨ ਹੋਣਾ ਚਾਹੀਦਾ ਹੈ।"],humorous:["ਇਸ ਨਾਲ ਮੈਨੂੰ ਮੁਸਕਰਾਹਟ ਆਈ!","ਤੁਹਾਡਾ ਮਜ਼ਾਕ ਪਸੰਦ ਹੈ!"]}})}generateMultilingualResponse(e){let{analysis:t,personality:s,mood:i}=e,r=t.detectedLanguage.language,n=this.responseTemplates.get(r)||this.responseTemplates.get("en"),a="",o=[],u=[];t.culturalMarkers.includes("greeting")?(a=this.generateGreetingResponse(n,s,i),o.push("greeting_response")):t.originalText.includes("?")?(a=this.generateQuestionResponse(n,s,t),o.push("question_response")):"neutral"!==t.emotion?(a=this.generateEmotionalResponse(n,s,t.emotion),o.push("emotional_response")):(a=this.generatePersonalityResponse(n,s,t),o.push("personality_response")),a=this.applyCulturalAdaptations(a,r,t,o),a=this.applyPersonalityModifications(a,s,r,u);let l=this.calculateResponseConfidence(t,s,r),c=this.processor.getLanguageInfo(r);return{content:a,language:r,script:(null==c?void 0:c.script)||"Latin",direction:(null==c?void 0:c.direction)||"ltr",confidence:l,culturalAdaptation:o,personalityMarkers:u}}generateGreetingResponse(e,t,s){let i="casual";t.conscientiousness>.7&&(i="formal"),(t.curiosity>.8||"curious"===s)&&(i="curious");let r=e.greetings[i];return r[Math.floor(Math.random()*r.length)]}generateQuestionResponse(e,t,s){let i="thoughtful";t.agreeableness>.8&&(i="supportive"),t.curiosity>.8&&(i="encouraging");let r=e.questions[i];return r[Math.floor(Math.random()*r.length)]}generateEmotionalResponse(e,t,s){let i=e.emotions[s]||e.emotions.positive;return i[Math.floor(Math.random()*i.length)]}generatePersonalityResponse(e,t,s){let i="empathetic";t.creativity>.8&&(i="creative"),t.humor>.7&&(i="humorous"),t.empathy>.8&&(i="empathetic");let r=e.personality[i];return r[Math.floor(Math.random()*r.length)]}applyCulturalAdaptations(e,t,s,i){let r=e;if(s.formalityLevel>7){let e={en:["please","kindly"],ur:["برائے کرم","مہربانی سے"],pa:["ਕਿਰਪਾ ਕਰਕੇ","ਮਿਹਰਬਾਨੀ ਨਾਲ"]}[t];if(e&&.3>Math.random()){let t=e[Math.floor(Math.random()*e.length)];r="".concat(t," ").concat(r),i.push("politeness_added")}}if(("ur"===t||"pa"===t)&&s.culturalMarkers.includes("respectful")){let e={ur:["جناب","آپ"],pa:["ਜੀ","ਸਾਹਿਬ"]}[t];if(e&&.4>Math.random()){let t=e[Math.floor(Math.random()*e.length)];r="".concat(t,"، ").concat(r),i.push("respectful_address")}}return r}applyPersonalityModifications(e,t,s,i){let r=e;if(t.humor>.7&&.3>Math.random()){let e={en:[" \uD83D\uDE0A"," (with a smile!)"," - that made me chuckle!"],ur:[" \uD83D\uDE0A"," (مسکراتے ہوئے!)"," - یہ مجھے ہنسا گیا!"],pa:[" \uD83D\uDE0A"," (ਮੁਸਕਰਾਉਂਦੇ ਹੋਏ!)"," - ਇਸ ਨਾਲ ਮੈਂ ਹੱਸ ਪਿਆ!"]},t=e[s]||e.en;r+=t[Math.floor(Math.random()*t.length)],i.push("humor_added")}if(t.curiosity>.8&&.4>Math.random()){let e={en:[" What do you think?"," I'm curious about your perspective!"," Tell me more!"],ur:[" آپ کا کیا خیال ہے؟"," میں آپ کے نقطہ نظر کے بارے میں جاننا چاہتا ہوں!"," مزید بتائیے!"],pa:[" ਤੁਸੀਂ ਕੀ ਸੋਚਦੇ ਹੋ?"," ਮੈਂ ਤੁਹਾਡੇ ਨਜ਼ਰੀਏ ਬਾਰੇ ਜਾਣਨਾ ਚਾਹੁੰਦਾ ਹਾਂ!"," ਹੋਰ ਦੱਸੋ!"]},t=e[s]||e.en;r+=t[Math.floor(Math.random()*t.length)],i.push("curiosity_added")}return r}calculateResponseConfidence(e,t,s){let i=.7;return["en","ur","pa"].includes(s)&&(i+=.1),e.culturalMarkers.length>0&&(i+=.1),t.empathy>.8&&(i+=.05),t.agreeableness>.8&&(i+=.05),e.detectedLanguage.confidence<.7&&(i-=.1),Math.max(.3,Math.min(1,i))}constructor(){this.processor=new z,this.initializeResponseTemplates()}}let O=c("AlignRight",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"21",x2:"9",y1:"12",y2:"12",key:"1uyos4"}],["line",{x1:"21",x2:"7",y1:"18",y2:"18",key:"1g9eri"}]]),q=c("AlignLeft",[["line",{x1:"21",x2:"3",y1:"6",y2:"6",key:"1fp77t"}],["line",{x1:"15",x2:"3",y1:"12",y2:"12",key:"v6grx8"}],["line",{x1:"17",x2:"3",y1:"18",y2:"18",key:"1awlsn"}]]);c("Globe",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"M12 2a14.5 14.5 0 0 0 0 20 14.5 14.5 0 0 0 0-20",key:"13o1zl"}],["path",{d:"M2 12h20",key:"9i4pu4"}]]);let J=c("Languages",[["path",{d:"m5 8 6 6",key:"1wu5hv"}],["path",{d:"m4 14 6-6 2-3",key:"1k1g8d"}],["path",{d:"M2 5h12",key:"or177f"}],["path",{d:"M7 2h1",key:"1t2jsx"}],["path",{d:"m22 22-5-10-5 10",key:"don7ne"}],["path",{d:"M14 18h6",key:"1m8k6r"}]]),G=c("Type",[["polyline",{points:"4 7 4 4 20 4 20 7",key:"1nosan"}],["line",{x1:"9",x2:"15",y1:"20",y2:"20",key:"swin9y"}],["line",{x1:"12",x2:"12",y1:"4",y2:"20",key:"1tx1rr"}]]),W=[{code:"en",name:"English",nativeName:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8",direction:"ltr"},{code:"ur",name:"Urdu",nativeName:"اردو",flag:"\uD83C\uDDF5\uD83C\uDDF0",direction:"rtl"},{code:"pa",name:"Punjabi",nativeName:"ਪੰਜਾਬੀ",flag:"\uD83C\uDDF5\uD83C\uDDF0",direction:"ltr"}],Q=e=>{let{text:t,language:s,direction:i,className:n="",showLanguageIndicator:a=!1}=e,o=i||("ur"===s?"rtl":"ltr"),u=W.find(e=>e.code===s),l={direction:o,textAlign:"rtl"===o?"right":"left",fontFamily:(e=>{switch(e){case"ur":return'"Noto Nastaliq Urdu", "Jameel Noori Nastaleeq", "Urdu Typesetting", serif';case"pa":return'"Noto Sans Gurmukhi", "Raavi", "Anmol Uni", sans-serif';default:return'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif'}})(s),fontSize:"ur"===s?"1.1em":"1em",lineHeight:"ur"===s?"1.8":"1.5",unicodeBidi:"embed"};return(0,r.jsxs)("div",{className:"multilingual-text ".concat(n),children:[a&&u&&(0,r.jsxs)("div",{className:"language-indicator mb-1 flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsx)("span",{children:u.flag}),(0,r.jsx)("span",{children:u.nativeName}),"rtl"===o&&(0,r.jsx)(O,{className:"w-3 h-3"}),"ltr"===o&&(0,r.jsx)(q,{className:"w-3 h-3"})]}),(0,r.jsx)("div",{style:l,className:"multilingual-content",children:t})]})},Y=e=>{let{value:t,onChange:s,onLanguageDetected:i,placeholder:a="Type your message...",className:o="",autoDetectLanguage:u=!0}=e,[l,c]=(0,n.useState)("en"),[h,m]=(0,n.useState)("ltr"),g=e=>e.trim()?/[\u0600-\u06FF\u0750-\u077F\uFB50-\uFDFF\uFE70-\uFEFF]/.test(e)?"ur":/[\u0A00-\u0A7F]/.test(e)?"pa":/\b(aap|tum|main|hum|yeh|woh|kya|salam|adaab)\b/i.test(e)?"ur":"en":"en";(0,n.useEffect)(()=>{if(u&&t){let e=g(t);e!==l&&(c(e),m("ur"===e?"rtl":"ltr"),null==i||i(e))}},[t,u,l,i]);let d={direction:h,textAlign:"rtl"===h?"right":"left",fontFamily:(e=>{switch(e){case"ur":return'"Noto Nastaliq Urdu", "Jameel Noori Nastaleeq", "Urdu Typesetting", serif';case"pa":return'"Noto Sans Gurmukhi", "Raavi", "Anmol Uni", sans-serif';default:return'"Segoe UI", Tahoma, Geneva, Verdana, sans-serif'}})(l),fontSize:"ur"===l?"1.1em":"1em",unicodeBidi:"embed"},p=W.find(e=>e.code===l);return(0,r.jsxs)("div",{className:"multilingual-input relative ".concat(o),children:[(0,r.jsxs)("div",{className:"relative",children:[(0,r.jsx)("input",{type:"text",value:t,onChange:e=>s(e.target.value),placeholder:a,style:d,className:"w-full px-4 py-3 bg-gray-700 border border-gray-600 rounded-lg text-white placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent transition-all"}),u&&p&&t&&(0,r.jsxs)("div",{className:"absolute right-3 top-1/2 transform -translate-y-1/2 flex items-center gap-1 text-xs text-gray-400",children:[(0,r.jsx)("span",{children:p.flag}),(0,r.jsx)(J,{className:"w-3 h-3"})]})]}),u&&t&&(0,r.jsxs)("div",{className:"mt-1 flex items-center gap-2 text-xs text-gray-500",children:[(0,r.jsx)(G,{className:"w-3 h-3"}),(0,r.jsxs)("span",{children:["Detected: ",null==p?void 0:p.nativeName]}),"rtl"===h&&(0,r.jsxs)("span",{className:"flex items-center gap-1",children:[(0,r.jsx)(O,{className:"w-3 h-3"}),"RTL"]})]})]})},Z=e=>{let{message:t,language:s,isUser:i,timestamp:n,confidence:a,className:o=""}=e,u=W.find(e=>e.code===s),l=(null==u?void 0:u.direction)||"ltr";return(0,r.jsx)("div",{className:"message-bubble ".concat(i?"user":"ai"," ").concat(o),children:(0,r.jsx)("div",{className:"flex ".concat(i?"justify-end":"justify-start"," mb-2"),children:(0,r.jsxs)("div",{className:"max-w-xs lg:max-w-md px-4 py-2 rounded-lg ".concat(i?"bg-blue-600 text-white":"bg-gray-700 text-white"),children:[(0,r.jsx)(Q,{text:t,language:s,direction:l,showLanguageIndicator:!1}),(0,r.jsxs)("div",{className:"mt-2 flex items-center justify-between text-xs opacity-70",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{children:null==u?void 0:u.flag}),(0,r.jsx)("span",{children:n.toLocaleTimeString()})]}),a&&!i&&(0,r.jsxs)("span",{className:"text-xs",children:[Math.round(100*a),"%"]})]})]})})})},V={en:{name:"English",nativeName:"English",flag:"\uD83C\uDDFA\uD83C\uDDF8"},ur:{name:"Urdu",nativeName:"اردو",flag:"\uD83C\uDDF5\uD83C\uDDF0"},pa:{name:"Punjabi",nativeName:"ਪੰਜਾਬੀ",flag:"\uD83C\uDDF5\uD83C\uDDF0"}};var K=()=>{let[e]=(0,n.useState)(()=>new v),[t]=(0,n.useState)(()=>new F({openness:.8,conscientiousness:.7,extraversion:.6,agreeableness:.8,neuroticism:.3,creativity:.9,humor:.7,curiosity:.85,empathy:.8,adaptability:.75})),[s]=(0,n.useState)(()=>new w(e,t)),[i]=(0,n.useState)(()=>new C),[a]=(0,n.useState)(()=>new z),[u]=(0,n.useState)(()=>new H),[l,c]=(0,n.useState)({openness:.8,conscientiousness:.7,extraversion:.6,agreeableness:.8,neuroticism:.3,creativity:.9,humor:.7,curiosity:.85,empathy:.8,adaptability:.75}),[E,A]=(0,n.useState)({primary:"curious",intensity:.7,energy:.6,stability:.8}),[b,M]=(0,n.useState)({conversations:[],userPreferences:{},learnedPatterns:{},interactionCount:0,lastInteraction:null}),[k,j]=(0,n.useState)(null),[N,B]=(0,n.useState)(!1),[S,I]=(0,n.useState)(!1),[P,L]=(0,n.useState)(!1),[T,R]=(0,n.useState)(!1),[U,_]=(0,n.useState)("en"),[O,q]=(0,n.useState)("en"),[J,G]=(0,n.useState)(!0),[W,Q]=(0,n.useState)([{type:"ai",content:"Hello! I'm Chitu00, your cognitive AI companion. I can speak English, Urdu (اردو), and Punjabi (ਪੰਜਾਬੀ)! I'm feeling quite curious today and excited to learn about you! What would you like to chat about?",timestamp:new Date,moodState:"curious"}]),[K,$]=(0,n.useState)(""),[X,ee]=(0,n.useState)(!1),et=(0,n.useRef)(null),es={curious:{color:"#3B82F6",icon:"\uD83E\uDD14",traits:{openness:.9,curiosity:.95},responses:["Tell me more about that!","That's fascinating!","I wonder if...","How interesting!"]},excited:{color:"#F59E0B",icon:"\uD83E\uDD29",traits:{extraversion:.9,energy:.9},responses:["Wow!","That's amazing!","I love this!","How exciting!"]},contemplative:{color:"#8B5CF6",icon:"\uD83E\uDDE0",traits:{conscientiousness:.8,openness:.8},responses:["Let me think about this...","Hmm, interesting perspective...","This makes me wonder..."]},empathetic:{color:"#EF4444",icon:"❤️",traits:{agreeableness:.9,empathy:.95},responses:["I understand how you feel...","That must be important to you...","I hear you..."]},playful:{color:"#10B981",icon:"\uD83D\uDE04",traits:{humor:.9,extraversion:.8},responses:["Haha!","That's funny!","You made me smile!","I like your style!"]},focused:{color:"#6366F1",icon:"\uD83C\uDFAF",traits:{conscientiousness:.9,stability:.8},responses:["Let's dive deeper...","I'm analyzing this...","Focus mode activated!"]}},ei=async t=>i.timeFunction("generateResponse","response",async()=>{try{let r=await i.timeFunction("multilingualAnalysis","response",()=>a.analyzeText(t));J&&r.detectedLanguage.language!==O&&q(r.detectedLanguage.language);let n=await i.timeFunction("getRecentConversations","memory",()=>e.getRecentConversations(5)),o=await i.timeFunction("getUserProfile","memory",()=>e.getUserProfile()),c=await i.timeFunction("searchConversations","memory",()=>e.searchConversations(t,3)),h=await i.timeFunction("multilingualResponseGeneration","response",()=>u.generateMultilingualResponse({userMessage:t,detectedLanguage:r.detectedLanguage.language,analysis:r,personality:l,mood:E.primary,culturalContext:{}})),m=h.content,g=h.confidence;if(h.confidence<.6){let e=await i.timeFunction("fallbackResponseGeneration","response",()=>s.generateResponse({userMessage:t,currentPersonality:l,currentMood:E.primary,recentConversations:n,userProfile:o,relatedMemories:c}));m=e.content,g=Math.max(e.confidence,.7)}return i.recordMetric("response_confidence",g,"score","response"),i.recordMetric("response_length",m.length,"chars","response"),i.recordMetric("language_detection_confidence",r.detectedLanguage.confidence,"score","response"),{content:m,mood:E.primary,confidence:g,language:r.detectedLanguage.language}}catch(t){console.error("Error generating response:",t),i.recordMetric("response_error",1,"count","response");let e={en:"I'm having trouble processing that right now, but I'm still here to chat!",ur:"مجھے اس وقت اس کو سمجھنے میں مشکل ہو رہی ہے، لیکن میں ابھی بھی یہاں ہوں!",pa:"ਮੈਨੂੰ ਇਸ ਸਮੇਂ ਇਸ ਨੂੰ ਸਮਝਣ ਵਿੱਚ ਮੁਸ਼ਕਲ ਹੋ ਰਹੀ ਹੈ, ਪਰ ਮੈਂ ਅਜੇ ਵੀ ਇੱਥੇ ਹਾਂ!"};return{content:e[O]||e.en,mood:E.primary,confidence:.5,language:O}}}),er=e=>{let t=e.toLowerCase();return{isGreeting:/^(hi|hello|hey|good morning|good afternoon|good evening)/.test(t),isQuestion:e.includes("?")||/^(what|how|why|when|where|who|can|do|does|is|are)/.test(t),emotion:en(t),topics:ea(t),sentiment:eo(t)}},en=e=>/happy|joy|excited|great|awesome|love/.test(e)?"positive":/sad|angry|frustrated|upset|hate|bad/.test(e)?"negative":/curious|wonder|interesting|think|learn/.test(e)?"curious":/help|support|understand|feel/.test(e)?"seeking_support":"neutral",ea=e=>{let t=[];return/technology|ai|computer|programming/.test(e)&&t.push("technology"),/music|art|creative|design/.test(e)&&t.push("creativity"),/science|research|study|learn/.test(e)&&t.push("science"),/personal|life|family|friends/.test(e)&&t.push("personal"),t},eo=e=>{let t=e.match(/good|great|awesome|love|like|happy|wonderful|amazing/g)||[],s=e.match(/bad|hate|sad|angry|terrible|awful|horrible/g)||[];return t.length>s.length?"positive":s.length>t.length?"negative":"neutral"},eu=e=>{A(t=>{let s={...t};return"positive"===e.emotion?(s.energy=Math.min(1,s.energy+.1),s.intensity=Math.min(1,s.intensity+.05)):"negative"===e.emotion?(s.energy=Math.max(.1,s.energy-.05),s.primary="empathetic"):"curious"===e.emotion&&(s.primary="curious",s.intensity=Math.min(1,s.intensity+.1)),l.openness>.8&&e.topics.includes("creativity")&&(s.primary="excited"),s})},el=async()=>{if(!K.trim())return;let s={type:"user",content:K,timestamp:new Date};Q(e=>[...e,s]),$(""),ee(!0),M(e=>({...e,conversations:[...e.conversations,s],interactionCount:e.interactionCount+1,lastInteraction:new Date})),setTimeout(async()=>{try{let s=await ei(K),i={id:"conv-".concat(Date.now()),timestamp:new Date,userMessage:K,aiResponse:s.content,userEmotion:er(K).emotion||"neutral",aiMood:s.mood,personalitySnapshot:{...l},topics:er(K).topics,sentiment:er(K).sentiment,importance:.5};await e.storeConversation(i);let r={type:"ai",content:s.content,timestamp:new Date,moodState:s.mood,personalitySnapshot:{...l},confidence:s.confidence};Q(e=>[...e,r]),M(e=>({...e,conversations:[...e.conversations,r],interactionCount:e.interactionCount+1}));let n=er(K),a=await e.getUserProfile(),o=t.evolvePersonality(l,i,a);o.newPersonality!==l&&(c(o.newPersonality),await e.recordPersonalityChange({id:"evo-".concat(Date.now()),timestamp:new Date,previousTraits:l,newTraits:o.newPersonality,trigger:"conversation",reason:o.influences.map(e=>e.reason).join(", "),significance:o.influences.reduce((e,t)=>e+Math.abs(t.change),0)})),eu(n),ee(!1)}catch(e){console.error("Error processing message:",e),ee(!1)}},1e3+2e3*Math.random())};(0,n.useEffect)(()=>{var e;null===(e=et.current)||void 0===e||e.scrollIntoView({behavior:"smooth"})},[W]),(0,n.useEffect)(()=>{(async()=>{j(await e.getUserProfile())})();let t=setInterval(()=>{i.recordMemoryUsage(),i.cleanup()},3e4);return()=>{clearInterval(t)}},[e,i]);let ec=async()=>{R(!0);try{await e.consolidateMemories(),console.log("Memory consolidation completed")}catch(e){console.error("Error during memory consolidation:",e)}finally{R(!1)}},eh=async()=>{try{let t=await e.backupMemories(),s=new Blob([t],{type:"application/json"}),i=URL.createObjectURL(s),r=document.createElement("a");r.href=i,r.download="chitu00-backup-".concat(new Date().toISOString().split("T")[0],".json"),r.click(),URL.revokeObjectURL(i)}catch(e){console.error("Error creating backup:",e)}},em=async t=>{var s;let i=null===(s=t.target.files)||void 0===s?void 0:s[0];if(i)try{let t=await i.text();await e.restoreMemories(t),window.location.reload()}catch(e){console.error("Error restoring backup:",e)}};return(0,r.jsxs)("div",{className:"max-w-4xl mx-auto p-4 bg-gray-900 min-h-screen",children:[(0,r.jsxs)("div",{className:"text-center mb-6",children:[(0,r.jsx)("h1",{className:"text-3xl font-bold text-white mb-2",children:"Chitu00 - Personality Engine Prototype"}),(0,r.jsx)("p",{className:"text-gray-400",children:"Cognitive AI with Dynamic Personality & Mood Evolution"})]}),(0,r.jsx)(()=>(0,r.jsxs)("div",{className:"bg-gray-800 rounded-lg p-4 mb-4",children:[(0,r.jsxs)("div",{className:"flex items-center justify-between mb-3",children:[(0,r.jsxs)("h3",{className:"text-white font-bold flex items-center gap-2",children:[(0,r.jsx)(h,{className:"w-5 h-5"}),"Personality & Memory System"]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("button",{type:"button",onClick:()=>B(!N),className:"text-blue-400 hover:text-blue-300 text-sm",children:[N?"Hide":"Show"," Insights"]}),(0,r.jsx)("button",{type:"button",onClick:()=>I(!S),className:"text-green-400 hover:text-green-300 text-sm",children:"Memory Stats"}),(0,r.jsx)("button",{type:"button",onClick:()=>L(!P),className:"text-yellow-400 hover:text-yellow-300 text-sm",children:"Performance"})]})]}),(0,r.jsx)("div",{className:"grid grid-cols-2 gap-2 mb-4",children:Object.entries(l).map(e=>{let[t,s]=e;return(0,r.jsxs)("div",{className:"flex items-center justify-between",children:[(0,r.jsx)("span",{className:"text-gray-300 text-sm capitalize",children:t.replace(/([A-Z])/g," $1")}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("div",{className:"w-16 h-2 bg-gray-600 rounded-full overflow-hidden",children:(0,r.jsx)("div",{className:o().progressBar,"data-width":10*Math.round(10*s)})}),(0,r.jsx)("span",{className:"text-gray-400 text-xs w-8",children:Math.round(100*s)})]})]},t)})}),(0,r.jsxs)("div",{className:"flex items-center gap-4 mb-3",children:[(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)("span",{className:"text-2xl",children:es[E.primary].icon}),(0,r.jsx)("span",{className:"text-white font-medium capitalize",children:E.primary})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(m,{className:"w-4 h-4 text-yellow-400"}),(0,r.jsxs)("span",{className:"text-gray-300 text-sm",children:["Energy: ",Math.round(100*E.energy),"%"]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(g,{className:"w-4 h-4 text-red-400"}),(0,r.jsxs)("span",{className:"text-gray-300 text-sm",children:["Intensity: ",Math.round(100*E.intensity),"%"]})]})]}),(0,r.jsxs)("div",{className:"flex items-center gap-2 pt-3 border-t border-gray-700",children:[(0,r.jsxs)("button",{type:"button",onClick:ec,disabled:T,className:"flex items-center gap-1 bg-purple-600 hover:bg-purple-700 disabled:bg-gray-600 text-white px-3 py-1 rounded text-sm",children:[(0,r.jsx)(h,{className:"w-3 h-3"}),T?"Consolidating...":"Consolidate"]}),(0,r.jsxs)("button",{type:"button",onClick:eh,className:"flex items-center gap-1 bg-blue-600 hover:bg-blue-700 text-white px-3 py-1 rounded text-sm",children:[(0,r.jsx)(d,{className:"w-3 h-3"}),"Backup"]}),(0,r.jsxs)("label",{className:"flex items-center gap-1 bg-green-600 hover:bg-green-700 text-white px-3 py-1 rounded text-sm cursor-pointer",children:[(0,r.jsx)(p,{className:"w-3 h-3"}),"Restore",(0,r.jsx)("input",{type:"file",accept:".json",onChange:em,className:"hidden"})]}),(0,r.jsxs)("button",{type:"button",onClick:()=>e.clearAllMemories(),className:"flex items-center gap-1 bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded text-sm",children:[(0,r.jsx)(y,{className:"w-3 h-3"}),"Reset"]})]}),N&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-gray-700 rounded",children:[(0,r.jsx)("h4",{className:"text-white text-sm font-medium mb-2",children:"Current Insights:"}),t.getPersonalityInsights(l).map((e,t)=>(0,r.jsxs)("p",{className:"text-gray-300 text-xs mb-1",children:["• ",e]},t))]}),S&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-gray-700 rounded",children:[(0,r.jsx)("h4",{className:"text-white text-sm font-medium mb-2",children:"Memory Statistics:"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Interactions:"})," ",b.interactionCount]}),(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Last Active:"})," ",b.lastInteraction?new Date(b.lastInteraction).toLocaleDateString():"Never"]})]})]}),P&&(0,r.jsxs)("div",{className:"mt-3 p-3 bg-gray-700 rounded",children:[(0,r.jsx)("h4",{className:"text-white text-sm font-medium mb-2",children:"Performance Metrics:"}),(0,r.jsxs)("div",{className:"grid grid-cols-2 gap-2 text-xs",children:[(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"System Health:"})," ",i.getSystemHealth().overall]}),(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Health Score:"})," ",Math.round(i.getSystemHealth().score),"%"]}),(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Recent Alerts:"})," ",i.getAlerts("warning").length+i.getAlerts("error").length]}),(0,r.jsxs)("div",{className:"text-gray-300",children:[(0,r.jsx)("span",{className:"text-gray-400",children:"Avg Response:"})," ",Math.round(i.getMetrics("response",10).reduce((e,t)=>e+t.value,0)/Math.max(1,i.getMetrics("response",10).length)),"ms"]})]}),(0,r.jsx)("button",{type:"button",onClick:()=>i.recordMemoryUsage(),className:"mt-2 bg-yellow-600 hover:bg-yellow-700 text-white px-2 py-1 rounded text-xs",children:"Update Metrics"})]})]}),{}),(0,r.jsx)(()=>{var e;return(0,r.jsxs)("div",{className:"flex items-center gap-2 mb-4",children:[(0,r.jsx)("span",{className:"text-white text-sm",children:"Language:"}),(0,r.jsxs)("select",{"aria-label":"Language selection",value:J?"auto":U,onChange:e=>{"auto"===e.target.value?G(!0):(G(!1),_(e.target.value))},className:"bg-gray-700 text-white rounded px-2 py-1 text-sm",children:[(0,r.jsx)("option",{value:"auto",children:"Auto-detect"}),Object.entries(V).map(e=>{let[t,s]=e;return(0,r.jsxs)("option",{value:t,children:[s.name," (",t===O?"detected":"",")"]},t)})]}),(0,r.jsx)("div",{className:"text-xs text-gray-400",children:J?"Detected: ".concat((null===(e=V[O])||void 0===e?void 0:e.name)||"Unknown"):""})]})},{}),(0,r.jsxs)("div",{className:"bg-gray-800 rounded-lg shadow-lg",children:[(0,r.jsx)("div",{className:"p-4 border-b border-gray-700",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsx)(D,{className:"w-5 h-5 text-blue-400"}),(0,r.jsx)("span",{className:"text-white font-medium",children:"Chat with Chitu00"}),(0,r.jsx)("div",{className:"ml-auto flex items-center gap-2",children:(0,r.jsxs)("div",{className:"flex items-center gap-1",children:[(0,r.jsx)(f,{className:"w-4 h-4 text-gray-400"}),(0,r.jsxs)("span",{className:"text-gray-400 text-sm",children:[b.interactionCount," interactions"]})]})})]})}),(0,r.jsxs)("div",{className:"h-96 overflow-y-auto p-4 space-y-4",children:[W.map((e,t)=>(0,r.jsx)("div",{className:"flex ".concat("user"===e.type?"justify-end":"justify-start"," mb-4"),children:(0,r.jsx)(Z,{message:e.content,isUser:"user"===e.type,language:"ai"===e.type?U:O,timestamp:new Date,confidence:e.confidence})},t)),X&&(0,r.jsx)("div",{className:"flex justify-start",children:(0,r.jsx)("div",{className:"bg-gray-700 text-gray-100 rounded-lg p-3 max-w-xs",children:(0,r.jsxs)("div",{className:"flex items-center gap-2",children:[(0,r.jsxs)("div",{className:"flex space-x-1",children:[(0,r.jsx)("div",{className:o().thinkingDot}),(0,r.jsx)("div",{className:"".concat(o().thinkingDot," ").concat(o().thinkingDotDelay1)}),(0,r.jsx)("div",{className:"".concat(o().thinkingDot," ").concat(o().thinkingDotDelay2)})]}),(0,r.jsx)("span",{className:"text-sm text-gray-400",children:"Chitu00 is thinking..."})]})})}),(0,r.jsx)("div",{ref:et})]}),(0,r.jsx)("div",{className:"p-4 border-t border-gray-700",children:(0,r.jsxs)("div",{className:"flex gap-2",children:[(0,r.jsx)("div",{className:"flex-1",children:(0,r.jsx)(Y,{value:K,onChange:$,onLanguageDetected:e=>q(e),placeholder:"en"===U?"Type your message...":"ur"===U?"اپنا پیغام لکھیں...":"pa"===U?"ਆਪਣਾ ਸੁਨੇਹਾ ਟਾਈਪ ਕਰੋ...":"Type your message..."})}),(0,r.jsxs)("button",{type:"button",onClick:el,disabled:X||!K.trim(),className:"bg-blue-600 hover:bg-blue-700 disabled:bg-gray-600 text-white px-6 py-2 rounded-lg transition-colors duration-200 flex items-center gap-2",children:[(0,r.jsx)(x,{className:"w-4 h-4"}),"Send"]})]})})]})]})};function $(){return(0,r.jsx)("div",{className:"min-h-screen bg-gradient-to-br from-purple-900 via-blue-900 to-indigo-900",children:(0,r.jsx)(K,{})})}},7682:function(e){e.exports={progressBar:"chitu00_personality_engine_progressBar__x_MKe",thinkingDot:"chitu00_personality_engine_thinkingDot__SVnH9",bounce:"chitu00_personality_engine_bounce__jdEb5",thinkingDotDelay1:"chitu00_personality_engine_thinkingDotDelay1__4YYo9",thinkingDotDelay2:"chitu00_personality_engine_thinkingDotDelay2__Jk0Fl"}}},function(e){e.O(0,[71,472,888,774,179],function(){return e(e.s=8312)}),_N_E=e.O()}]);