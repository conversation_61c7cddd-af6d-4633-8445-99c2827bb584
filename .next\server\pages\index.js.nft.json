{"version": 1, "files": ["../../../node_modules/client-only/index.js", "../../../node_modules/client-only/package.json", "../../../node_modules/collapse-white-space/index.js", "../../../node_modules/collapse-white-space/package.json", "../../../node_modules/franc/data.js", "../../../node_modules/franc/expressions.js", "../../../node_modules/franc/index.js", "../../../node_modules/franc/package.json", "../../../node_modules/n-gram/index.js", "../../../node_modules/n-gram/package.json", "../../../node_modules/next/dist/compiled/@opentelemetry/api/index.js", "../../../node_modules/next/dist/compiled/@opentelemetry/api/package.json", "../../../node_modules/next/dist/compiled/jsonwebtoken/index.js", "../../../node_modules/next/dist/compiled/jsonwebtoken/package.json", "../../../node_modules/next/dist/compiled/next-server/pages.runtime.prod.js", "../../../node_modules/next/dist/compiled/node-html-parser/index.js", "../../../node_modules/next/dist/compiled/node-html-parser/package.json", "../../../node_modules/next/dist/lib/semver-noop.js", "../../../node_modules/next/dist/server/lib/trace/constants.js", "../../../node_modules/next/dist/server/lib/trace/tracer.js", "../../../node_modules/next/package.json", "../../../node_modules/react-dom/cjs/react-dom-server-legacy.browser.development.js", "../../../node_modules/react-dom/cjs/react-dom-server-legacy.browser.production.min.js", "../../../node_modules/react-dom/cjs/react-dom-server.browser.development.js", "../../../node_modules/react-dom/cjs/react-dom-server.browser.production.min.js", "../../../node_modules/react-dom/package.json", "../../../node_modules/react-dom/server.browser.js", "../../../node_modules/react/cjs/react-jsx-runtime.development.js", "../../../node_modules/react/cjs/react-jsx-runtime.production.min.js", "../../../node_modules/react/cjs/react.development.js", "../../../node_modules/react/cjs/react.production.min.js", "../../../node_modules/react/index.js", "../../../node_modules/react/jsx-runtime.js", "../../../node_modules/react/package.json", "../../../node_modules/styled-jsx/dist/index/index.js", "../../../node_modules/styled-jsx/index.js", "../../../node_modules/styled-jsx/package.json", "../../../node_modules/transliteration/dist/node/data/charmap.js", "../../../node_modules/transliteration/dist/node/src/common/slugify.js", "../../../node_modules/transliteration/dist/node/src/common/transliterate.js", "../../../node_modules/transliteration/dist/node/src/common/utils.js", "../../../node_modules/transliteration/dist/node/src/node/index.js", "../../../node_modules/transliteration/package.json", "../../../node_modules/trigram-utils/index.js", "../../../node_modules/trigram-utils/package.json", "../../../package.json", "../../package.json", "../chunks/899.js", "../webpack-runtime.js"]}