// Multilingual Language Processing System for Chitu00
// Supports English, Urdu, and Punjabi languages

import { franc } from 'franc';
import { transliterate } from 'transliteration';

export interface LanguageCapability {
  code: string;           // 'en', 'ur', 'pa'
  name: string;          // 'English', 'Urdu', 'Punjabi'
  nativeName?: string;   // Name in the language itself
  script: string;        // 'Latin', 'Arabic', 'Gurmukhi'
  direction: 'ltr' | 'rtl';
  complexity: number;    // 1-10 scale
  dialects: string[];
  culturalContext?: {
    formalityLevel: number;
    emotionalExpression: number;
    humorStyle: string;
    respectPatterns: string[];
    greetings: string[];
    farewells: string[];
    politenessMarkers: string[];
  };
}

export const LANGUAGE_CAPABILITIES: Record<string, LanguageCapability> = {
  en: {
    code: 'en',
    name: 'English',
    script: 'Latin',
    direction: 'ltr',
    complexity: 5,
    dialects: ['American', 'British', 'Australian']
  },
  ur: {
    code: 'ur',
    name: 'Urdu',
    script: 'Arabic',
    direction: 'rtl',
    complexity: 8,
    dialects: ['Standard', 'Dakhini', '<PERSON>kh<PERSON>']
  },
  pa: {
    code: 'pa',
    name: 'Punjabi',
    script: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
    direction: 'ltr',
    complexity: 7,
    dialects: ['Majhi', 'Doabi', 'Malwai', 'Pothohari']
  }
};

export interface LanguageDetectionResult {
  language: string;
  confidence: number;
  script: string;
  direction: 'ltr' | 'rtl';
  isRomanized: boolean; // For Roman Urdu/Punjabi
}

export interface MultilingualAnalysis {
  detectedLanguage: LanguageDetectionResult;
  originalText: string;
  normalizedText: string;
  transliteration?: string;
  sentiment: string;
  emotion: string;
  topics: string[];
  culturalMarkers: string[];
  formalityLevel: number;
}

export class MultilingualProcessor {
  private supportedLanguages: Map<string, LanguageCapability>;
  private urduPatterns: RegExp[];
  private punjabi_patterns: RegExp[];
  private romanUrduPatterns: RegExp[];

  constructor() {
    this.supportedLanguages = new Map();
    this.initializeLanguages();
    this.initializePatterns();
  }

  private initializeLanguages(): void {
    // English
    this.supportedLanguages.set('en', {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      script: 'Latin',
      direction: 'ltr',
      complexity: 5,
      dialects: ['American', 'British', 'Pakistani English'],
      culturalContext: {
        formalityLevel: 5,
        emotionalExpression: 6,
        humorStyle: 'direct',
        respectPatterns: ['please', 'thank you', 'sir', 'madam'],
        greetings: ['hello', 'hi', 'good morning', 'good evening'],
        farewells: ['goodbye', 'bye', 'see you later', 'take care'],
        politenessMarkers: ['please', 'kindly', 'would you', 'could you']
      }
    });

    // Urdu
    this.supportedLanguages.set('ur', {
      code: 'ur',
      name: 'Urdu',
      nativeName: 'اردو',
      script: 'Arabic',
      direction: 'rtl',
      complexity: 8,
      dialects: ['Standard Urdu', 'Dakhini', 'Rekhta'],
      culturalContext: {
        formalityLevel: 8,
        emotionalExpression: 7,
        humorStyle: 'poetic',
        respectPatterns: ['آپ', 'جناب', 'صاحب', 'محترم'],
        greetings: ['السلام علیکم', 'آداب', 'نمسکار', 'صبح بخیر'],
        farewells: ['خدا حافظ', 'اللہ حافظ', 'پھر ملیں گے'],
        politenessMarkers: ['برائے کرم', 'مہربانی', 'التماس', 'عرض']
      }
    });

    // Punjabi
    this.supportedLanguages.set('pa', {
      code: 'pa',
      name: 'Punjabi',
      nativeName: 'ਪੰਜਾਬੀ',
      script: 'Gurmukhi',
      direction: 'ltr',
      complexity: 7,
      dialects: ['Majhi', 'Doabi', 'Malwai', 'Pothohari'],
      culturalContext: {
        formalityLevel: 6,
        emotionalExpression: 8,
        humorStyle: 'folk',
        respectPatterns: ['ਜੀ', 'ਸਾਹਿਬ', 'ਜੀ ਹਾਂ'],
        greetings: ['ਸਤ ਸ੍ਰੀ ਅਕਾਲ', 'ਨਮਸਕਾਰ', 'ਆਦਾਬ'],
        farewells: ['ਰੱਬ ਰਾਖਾ', 'ਫਿਰ ਮਿਲਾਂਗੇ'],
        politenessMarkers: ['ਕਿਰਪਾ ਕਰਕੇ', 'ਮਿਹਰਬਾਨੀ', 'ਜੀ']
      }
    });
  }

  private initializePatterns(): void {
    // Urdu script patterns (Arabic script)
    this.urduPatterns = [
      /[\u0600-\u06FF]/g,  // Arabic script range
      /[\u0750-\u077F]/g,  // Arabic supplement
      /[\uFB50-\uFDFF]/g,  // Arabic presentation forms A
      /[\uFE70-\uFEFF]/g   // Arabic presentation forms B
    ];

    // Punjabi Gurmukhi patterns
    this.punjabi_patterns = [
      /[\u0A00-\u0A7F]/g,  // Gurmukhi script range
    ];

    // Roman Urdu patterns (common Urdu words in Latin script)
    this.romanUrduPatterns = [
      /\b(aap|tum|main|hum|yeh|woh|kya|kaise|kahan|kyun)\b/gi,
      /\b(salam|adaab|khuda|hafiz|inshallah|mashallah)\b/gi,
      /\b(accha|theek|bilkul|zaroor|shukria|meherbani)\b/gi
    ];
  }

  // Main language detection function
  detectLanguage(text: string): LanguageDetectionResult {
    const cleanText = text.trim();
    
    if (!cleanText) {
      return {
        language: 'en',
        confidence: 0,
        script: 'Latin',
        direction: 'ltr',
        isRomanized: false
      };
    }

    // Check for Urdu script
    if (this.hasUrduScript(cleanText)) {
      return {
        language: 'ur',
        confidence: 0.9,
        script: 'Arabic',
        direction: 'rtl',
        isRomanized: false
      };
    }

    // Check for Punjabi Gurmukhi script
    if (this.hasPunjabiScript(cleanText)) {
      return {
        language: 'pa',
        confidence: 0.9,
        script: 'Gurmukhi',
        direction: 'ltr',
        isRomanized: false
      };
    }

    // Check for Roman Urdu
    if (this.isRomanUrdu(cleanText)) {
      return {
        language: 'ur',
        confidence: 0.7,
        script: 'Latin',
        direction: 'ltr',
        isRomanized: true
      };
    }

    // Use franc for other language detection
    try {
      const detected = franc(cleanText);
      if (detected && detected !== 'und') {
        return {
          language: detected === 'urd' ? 'ur' : detected === 'pan' ? 'pa' : 'en',
          confidence: 0.8,
          script: 'Latin',
          direction: 'ltr',
          isRomanized: false
        };
      }
    } catch (error) {
      console.warn('Language detection error:', error);
    }

    // Default to English
    return {
      language: 'en',
      confidence: 0.5,
      script: 'Latin',
      direction: 'ltr',
      isRomanized: false
    };
  }

  // Comprehensive text analysis
  analyzeText(text: string): MultilingualAnalysis {
    const detectedLanguage = this.detectLanguage(text);
    const languageInfo = this.supportedLanguages.get(detectedLanguage.language);
    
    let normalizedText = text.trim();
    let transliteration: string | undefined;

    // Handle transliteration for non-Latin scripts
    if (detectedLanguage.script !== 'Latin') {
      try {
        transliteration = transliterate(text);
      } catch (error) {
        console.warn('Transliteration error:', error);
      }
    }

    // Analyze sentiment and emotion
    const sentiment = this.analyzeSentiment(text, detectedLanguage.language);
    const emotion = this.analyzeEmotion(text, detectedLanguage.language);
    
    // Extract topics
    const topics = this.extractTopics(text, detectedLanguage.language);
    
    // Identify cultural markers
    const culturalMarkers = this.identifyCulturalMarkers(text, detectedLanguage.language);
    
    // Assess formality level
    const formalityLevel = this.assessFormality(text, detectedLanguage.language);

    return {
      detectedLanguage,
      originalText: text,
      normalizedText,
      transliteration,
      sentiment,
      emotion,
      topics,
      culturalMarkers,
      formalityLevel
    };
  }

  // Helper methods for script detection
  private hasUrduScript(text: string): boolean {
    return this.urduPatterns.some(pattern => pattern.test(text));
  }

  private hasPunjabiScript(text: string): boolean {
    return this.punjabi_patterns.some(pattern => pattern.test(text));
  }

  private isRomanUrdu(text: string): boolean {
    const matches = this.romanUrduPatterns.reduce((count, pattern) => {
      const found = text.match(pattern);
      return count + (found ? found.length : 0);
    }, 0);
    
    const words = text.split(/\s+/).length;
    return matches / words > 0.3; // 30% threshold for Roman Urdu
  }

  // Sentiment analysis (language-aware)
  private analyzeSentiment(text: string, language: string): string {
    const lowerText = text.toLowerCase();
    
    // Language-specific sentiment patterns
    const sentimentPatterns = {
      en: {
        positive: ['good', 'great', 'excellent', 'amazing', 'wonderful', 'love', 'like', 'happy'],
        negative: ['bad', 'terrible', 'awful', 'hate', 'sad', 'angry', 'disappointed']
      },
      ur: {
        positive: ['اچھا', 'بہترین', 'خوشی', 'محبت', 'پسند', 'شاندار'],
        negative: ['برا', 'غصہ', 'غم', 'ناراض', 'مایوس']
      },
      pa: {
        positive: ['ਚੰਗਾ', 'ਵਧੀਆ', 'ਖੁਸ਼ੀ', 'ਪਿਆਰ'],
        negative: ['ਮਾੜਾ', 'ਗੁੱਸਾ', 'ਦੁੱਖ']
      }
    };

    const patterns = sentimentPatterns[language as keyof typeof sentimentPatterns] || sentimentPatterns.en;
    
    const positiveCount = patterns.positive.filter(word => lowerText.includes(word)).length;
    const negativeCount = patterns.negative.filter(word => lowerText.includes(word)).length;
    
    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  // Emotion analysis (language-aware)
  private analyzeEmotion(text: string, language: string): string {
    const lowerText = text.toLowerCase();
    
    // Basic emotion detection patterns
    if (text.includes('?')) return 'curious';
    if (lowerText.includes('help') || lowerText.includes('مدد') || lowerText.includes('ਮਦਦ')) return 'seeking_support';
    
    const sentiment = this.analyzeSentiment(text, language);
    return sentiment === 'positive' ? 'happy' : sentiment === 'negative' ? 'sad' : 'neutral';
  }

  // Topic extraction (language-aware)
  private extractTopics(text: string, language: string): string[] {
    const topics: string[] = [];
    const lowerText = text.toLowerCase();
    
    // Universal topic patterns
    const topicPatterns = {
      technology: ['computer', 'ai', 'technology', 'کمپیوٹر', 'ٹیکنالوجی', 'ਕੰਪਿਊਟਰ'],
      family: ['family', 'mother', 'father', 'خاندان', 'ماں', 'باپ', 'ਪਰਿਵਾਰ'],
      work: ['work', 'job', 'کام', 'نوکری', 'ਕੰਮ'],
      health: ['health', 'doctor', 'صحت', 'ڈاکٹر', 'ਸਿਹਤ']
    };
    
    Object.entries(topicPatterns).forEach(([topic, keywords]) => {
      if (keywords.some(keyword => lowerText.includes(keyword))) {
        topics.push(topic);
      }
    });
    
    return topics;
  }

  // Cultural marker identification
  private identifyCulturalMarkers(text: string, language: string): string[] {
    const markers: string[] = [];
    const languageInfo = this.supportedLanguages.get(language);
    
    if (!languageInfo) return markers;
    
    const { respectPatterns, greetings, farewells, politenessMarkers } = languageInfo.culturalContext;
    
    // Check for respect patterns
    if (respectPatterns.some(pattern => text.includes(pattern))) {
      markers.push('respectful');
    }
    
    // Check for greetings
    if (greetings.some(greeting => text.includes(greeting))) {
      markers.push('greeting');
    }
    
    // Check for farewells
    if (farewells.some(farewell => text.includes(farewell))) {
      markers.push('farewell');
    }
    
    // Check for politeness
    if (politenessMarkers.some(marker => text.includes(marker))) {
      markers.push('polite');
    }
    
    return markers;
  }

  // Formality assessment
  private assessFormality(text: string, language: string): number {
    const languageInfo = this.supportedLanguages.get(language);
    if (!languageInfo) return 5;
    
    let formalityScore = languageInfo.culturalContext.formalityLevel;
    
    // Adjust based on cultural markers
    const markers = this.identifyCulturalMarkers(text, language);
    if (markers.includes('respectful')) formalityScore += 2;
    if (markers.includes('polite')) formalityScore += 1;
    
    return Math.min(10, Math.max(1, formalityScore));
  }

  // Get language information
  getLanguageInfo(languageCode: string): LanguageCapability | undefined {
    return this.supportedLanguages.get(languageCode);
  }

  // Get all supported languages
  getSupportedLanguages(): LanguageCapability[] {
    return Array.from(this.supportedLanguages.values());
  }

  // Check if language is supported
  isLanguageSupported(languageCode: string): boolean {
    return this.supportedLanguages.has(languageCode);
  }
}












